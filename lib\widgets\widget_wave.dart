import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

class WidgetWave extends StatelessWidget {
  const WidgetWave({super.key});

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: 1,
      child: SizedB<PERSON>(
        width: 200.w,
        height: 200.w,
        child: Lottie.asset('assets/images/wave-3.json'),
      ),
    );
  }
}
