{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "kadaya",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "kadaya (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "kadaya (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "aliyun_oss_flutter-1.1.1",
            "cwd": "plugin/aliyun_oss_flutter-1.1.1",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "aliyun_oss_flutter-1.1.1 (profile mode)",
            "cwd": "plugin/aliyun_oss_flutter-1.1.1",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "aliyun_oss_flutter-1.1.1 (release mode)",
            "cwd": "plugin/aliyun_oss_flutter-1.1.1",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter_webrtc-0.12.12",
            "cwd": "plugin/flutter_webrtc-0.12.12",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter_webrtc-0.12.12 (profile mode)",
            "cwd": "plugin/flutter_webrtc-0.12.12",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter_webrtc-0.12.12 (release mode)",
            "cwd": "plugin/flutter_webrtc-0.12.12",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "screen_size",
            "cwd": "plugin/screen_size",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "screen_size (profile mode)",
            "cwd": "plugin/screen_size",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "screen_size (release mode)",
            "cwd": "plugin/screen_size",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}