import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ConfigDialog extends StatefulWidget {
  final Function(bool isSaved, Map<String, String>? data)? onDismiss;

  Map<String, String>? defaultData;

  ConfigDialog({super.key, this.onDismiss, this.defaultData});

  @override
  State<ConfigDialog> createState() => _ConfigDialogState();
}

class _ConfigDialogState extends State<ConfigDialog> {
  String _selectedEnvironment = '测试';
  final TextEditingController _deviceController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  // final TextEditingController _algorithmServiceController = TextEditingController();
  // final TextEditingController _printServiceController = TextEditingController();

  @override
  void initState() {
    super.initState();

    _deviceController.text = widget.defaultData?['screenNum'] ?? '';
    _codeController.text = widget.defaultData?['deviceCode'] ?? '';
    _selectedEnvironment = widget.defaultData?['env'] == 'prod' ? '生产' : '测试';
  }

  @override
  void dispose() {
    _deviceController.dispose();
    _codeController.dispose();
    // _algorithmServiceController.dispose();
    // _printServiceController.dispose();
    super.dispose();
  }

  void _handleSave() {
    if (!_isValidate()) return;

    final data = {
      'screenNum': _deviceController.text,
      'deviceCode': _codeController.text,
      'env': _selectedEnvironment == '测试' ? 'dev' : 'prod',
    };
    if (widget.onDismiss != null) {
      widget.onDismiss!(true, data);
    }
    Navigator.of(context).pop();
  }

  void _handleCancel() {
    // 调用回调但不传递数据
    if (widget.onDismiss != null) {
      widget.onDismiss!(false, null);
    }

    Navigator.of(context).pop();
  }

  bool _isValidate() {
    return _deviceController.text != '' || _codeController.text != '';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                '设备设置',
                style: TextStyle(fontSize: 32.w, fontWeight: FontWeight.bold),
              ),
            ),
            const Divider(),

            // 设备号
            TextField(
              controller: _deviceController,
              decoration: const InputDecoration(
                labelText: '大屏设备码',
              ),
            ),
            SizedBox(height: 16.w),
            TextField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'Pad设备码',
              ),
            ),
            SizedBox(height: 16.w),

            // 环境下拉菜单
            Container(
              decoration: BoxDecoration(
                color: Colors.transparent,
                border: Border.all(color: Colors.grey),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  isExpanded: true,
                  value: _selectedEnvironment,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  icon: const Icon(Icons.arrow_drop_down),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedEnvironment = newValue!;
                    });
                  },
                  items: <String>['生产', '测试']
                      .map<DropdownMenuItem<String>>((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                ),
              ),
            ),

            SizedBox(height: 32.w),

            // 按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _handleCancel,
                  child: const Text('取消'),
                ),
                SizedBox(width: 8.w),
                ElevatedButton(
                  onPressed: _handleSave,
                  child: const Text('保存'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
