// lib/models/style_model.dart
import 'package:json_annotation/json_annotation.dart';

part 'style_model.g.dart';

@JsonSerializable()

class StyleModel {
  final int id;
  final int? agentId;
  final int? merchantId;
  final int? storeId;
  final int styleNum;
  final String styleName;
  final int sex;
  final String version;
  final String updateVersionTime;
  final String picCover;
  final List<PicCollect> picCollect;

  StyleModel({
    required this.id,
    this.agentId,
    this.merchantId,
    this.storeId,
    required this.styleNum,
    required this.styleName,
    required this.sex,
    required this.version,
    required this.updateVersionTime,
    required this.picCover,
    required this.picCollect,
  });

  factory StyleModel.fromJson(Map<String, dynamic> json) =>
      _$StyleModelFromJson(json);

  Map<String, dynamic> toJson() => _$StyleModelToJson(this);
}

@JsonSerializable()
class PicCollect {
  final int id;
  final String uuid;
  final String url;

  PicCollect({
    required this.id,
    required this.uuid,
    required this.url,
  });

  factory PicCollect.fromJson(Map<String, dynamic> json) =>
      _$PicCollectFromJson(json);

  Map<String, dynamic> toJson() => _$PicCollectToJson(this);
}