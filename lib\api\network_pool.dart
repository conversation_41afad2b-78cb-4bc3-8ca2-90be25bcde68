import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:kadaya/api/algorithm_service.dart';
import 'package:kadaya/utils/global.dart';

class NetworkRequestPool {
  final int maxConcurrent;
  final List<dynamic> requestIndices;
  final _resultController = StreamController<RequestResult>();

  int _completed = 0;
  int _nextIndex = 0;
  bool _cancel = false;

  // 获取结果的流
  Stream<RequestResult> get results => _resultController.stream;

  NetworkRequestPool({
    required this.maxConcurrent,
    required this.requestIndices,
  });

  void cancel() {
    _cancel = true;
  }

  // 开始执行所有请求
  void start() async {
    if (requestIndices.isEmpty) {
      _resultController.close();
      return;
    }

    // 初始启动最大并发数量的请求
    for (int i = 0; i < min(maxConcurrent, requestIndices.length); i++) {
      _executeNext();
    }
  }

  // 执行下一个请求
  void _executeNext() {
    if (_cancel) return;
    if (_nextIndex >= requestIndices.length) return;
    final params = requestIndices[_nextIndex++]['params'];
    params['isFirst'] = Global.instance.isFirst;
    Global.instance.isFirst = false;

    var url = 'http://127.0.0.1:8082/algorithmCreate';
    if (Platform.isAndroid) {
      url = Global.instance.appConfig.algorithmApiUrl ??
          'http://192.168.123.140:8920/algorithmCreate';
      url = url.trimRight();
    }

    // 执行网络请求
    NetworkUtils.postData(url, params).then((result) {
      _onResult(params['templates'][0]['uuid'], result, null);
    }).catchError((error) {
      _onResult(params['templates'][0]['uuid'], null, error);
    });
  }

  void _onResult(String uuid, dynamic result, dynamic error) {
    if (_resultController.isClosed) return;
    if (error != null) {
      _resultController.add(RequestResult(path: null, error: error.toString()));
    } else {
      _resultController
          .add(RequestResult(path: result['result'][0], uuid: uuid));
    }
    _completed++;
    if (_completed == requestIndices.length) {
      _resultController.close();
    } else if (_nextIndex < requestIndices.length) {
      _executeNext();
    }
  }

  // 释放资源
  void dispose() {
    if (!_resultController.isClosed) _resultController.close();
  }
}

class RequestResult {
  final String? path;
  final String? uuid;
  final String? error;

  RequestResult({
    this.path,
    this.uuid,
    this.error,
  });

  @override
  String toString() => 'RequestResult(result: $path, error: $error)';
}
