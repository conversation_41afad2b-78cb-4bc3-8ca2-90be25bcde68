name: flutter_webrtc
description: Flutter WebRTC plugin for iOS/Android/Destkop/Web, based on GoogleWebRTC.
version: 0.13.1+hotfix.1
homepage: https://github.com/cloudwebrtc/flutter-webrtc
environment:
  sdk: '>=3.3.0 <4.0.0'
  flutter: '>=1.22.0'

dependencies:
  collection: ^1.17.0
  dart_webrtc: ^1.5.3
  flutter:
    sdk: flutter
  path_provider: ^2.0.2
  web: ^1.0.0
  webrtc_interface: ^1.2.2+hotfix.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  import_sorter: ^4.6.0
  lints: ^4.0.0
  pedantic: ^1.11.1
  test: any

flutter:
  plugin:
    platforms:
      android:
        package: com.cloudwebrtc.webrtc
        pluginClass: FlutterWebRTCPlugin
      ios:
        pluginClass: FlutterWebRTCPlugin
      macos:
        pluginClass: FlutterWebRTCPlugin
      windows:
        pluginClass: FlutterWebRTCPlugin
      linux:
        pluginClass: FlutterWebRTCPlugin
      elinux:
        pluginClass: FlutterWebRTCPlugin
