// cart_page.dart
import 'dart:collection';
import 'dart:io';

import 'package:animate_do/animate_do.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kadaya/api/api_service.dart';
import 'package:kadaya/api/network_pool.dart';
import 'package:kadaya/models/cart_model.dart';
import 'package:kadaya/models/config_model.dart';
import 'package:kadaya/models/discount_model.dart';
import 'package:kadaya/models/product_model.dart';
import 'package:kadaya/models/style_model.dart';
import 'package:kadaya/pages/page_payment.dart';
import 'package:kadaya/utils/constants.dart';
import 'package:kadaya/utils/global.dart';
import 'package:kadaya/utils/sound_player.dart';
import 'package:kadaya/utils/toast.dart';
import 'package:kadaya/widgets/button_back.dart';
import 'package:kadaya/widgets/floating_countdown.dart';
import 'package:kadaya/widgets/network_state_wrapper.dart';
import 'package:lottie/lottie.dart';

import '../widgets/widget_wave.dart';

class PageCart extends StatefulWidget {
  const PageCart({super.key});

  @override
  State<PageCart> createState() => _PageCartState();
}

class _PageCartState extends State<PageCart> with RouteAware {
  String avatarPath = Global.instance.orignalPath;
  List<CartModel> carts = Global.instance.carts;
  StyleModel styleModel = Global.instance.currentStyleModel!;
  List<ProductModel> products = Global.instance.products;
  List<DiscountModel> discounts = List.of(Global.instance.discounts)
    ..sort((a, b) => a.priority.compareTo(b.priority));
  AppConfig appConfig = Global.instance.appConfig;
  late List<DiscountModel> discountsNow = _filterDiscountsNow();
  late NetworkRequestPool _requestPool;

  Decimal paymentTotal = Decimal.zero;
  Decimal paymentDiscount = Decimal.zero;
  Decimal originalAmount = Decimal.zero;
  int cardsCount = 0;
  bool isLoading = false;
  bool soundedSelect = false;

  int get cartsShowLength {
    for (var i = 0; i < carts.length; i++) {
      if (carts[i].loading) {
        return i == 0 ? 0 : i + 1;
      }
    }
    return carts.length;
  }

  late GlobalKey<FloatingCountdownState> floatingCountdownKey;

  // router
  var routeObserver = Global.instance.routeObserver;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void didPopNext() {
    // became visible again
    _didIn();
  }

  @override
  void didPushNext() {
    // new route over me
    _didOut();
  }

  void _didOut() {
    _requestPool.cancel();
    _requestPool.dispose();
    floatingCountdownKey.currentState?.stop();
  }

  void _didIn() {
    soundedSelect = false;
    _updateTotals();
    SoundPlayer().play('generateBegin');
    startRequest();
    floatingCountdownKey = GlobalKey<FloatingCountdownState>();
  }

  @override
  void initState() {
    super.initState();
    _didIn();
  }

  @override
  void dispose() {
    _didOut();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: kBgColor,
        body: NetworkStateWrapper(
          child: SafeArea(
              child: Stack(children: [
            Image.asset(
              "assets/images/bg-black.png",
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
            ),

            FloatingCountdown(
              key: floatingCountdownKey,
              seconds: 120,
              maxSeconds: 180,
              showRemainSeconds: 30,
              warningThreshold: 20,
              bottom: 0.35.sh,
              onCountdownEnd: () => backToHome(context),
            ),

            Column(
              children: [
                _buildLeaderTip(),
                cartsShowLength > 0
                    ? Expanded(
                        child: ShaderMask(
                          shaderCallback: (Rect bounds) {
                            final gradientHeight = 30.w;
                            final stopPosition = gradientHeight / bounds.height;
                            final bottomStopPosition = 1.0 - stopPosition;

                            return LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: const [
                                Colors.transparent,
                                Colors.black,
                                Colors.black,
                                Colors.transparent
                              ],
                              stops: [
                                0.0,
                                stopPosition.clamp(0.0, 0.5),
                                bottomStopPosition.clamp(0.5, 1.0),
                                1.0
                              ],
                            ).createShader(bounds);
                          },
                          blendMode: BlendMode.dstIn,
                          child: Container(
                            margin: EdgeInsets.only(left: 40.w, right: 40.w),
                            child: GridView.builder(
                                padding: EdgeInsets.only(
                                    bottom: .2.sh,
                                    left: 40.w,
                                    right: 40.w,
                                    top: 40.w),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  crossAxisSpacing: 60.w,
                                  mainAxisSpacing: 20.w,
                                  childAspectRatio: 2 / 3,
                                ),
                                itemCount: cartsShowLength,
                                itemBuilder: (context, index) {
                                  return _buildItem(index);
                                }),
                          ),
                        ),
                      )
                    : Expanded(child: Center(child: _buildLoading())),
                SizedBox(height: 70.w),
              ],
            ),

            // checkout
            if (cartsShowLength > 0)
              Positioned(left: 0, right: 0, bottom: 0, child: _buildCheckout())
                  .animate()
                  .fadeIn(
                    duration: const Duration(milliseconds: 510),
                  ),
          ])),
        ));
  }

  Widget _buildLoading() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          children: [
            SizedBox(
              width: 0.8.sw,
              height: 0.8.sw,
              child: Lottie.asset('assets/images/loading.json',
                  fit: BoxFit.contain),
            ),
            Text(
              "照片生成中",
              style: TextStyle(
                fontSize: 36.w,
                color: Colors.white,
              ),
            ),
            Text(
              "Generating photos",
              style: TextStyle(
                fontSize: 21.w,
                color: Colors.white,
              ),
            )
          ],
        ),
        Text(
          "....",
          style: TextStyle(
            fontSize: 21.w,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 350.w),
      ],
    );
  }

  Widget _buildLoading2() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          children: [
            Text(
              "照片生成中",
              style: TextStyle(
                fontSize: 60.w,
                color: Colors.white,
              ),
            ),
            Text(
              "Generating photos",
              style: TextStyle(
                fontSize: 30.w,
                color: Colors.white,
              ),
            )
          ],
        ),
        Text(
          "....",
          style: TextStyle(
            fontSize: 30.w,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 150.w),
        Container(
          width: 100.w,
          height: 100.w,
          child: CircularProgressIndicator(
            valueColor: const AlwaysStoppedAnimation<Color>(BTN_PRIMARY_COLOR),
            backgroundColor: Colors.grey[200],
            strokeWidth: 8.w,
          ),
        ),
        SizedBox(height: 170.w),
      ],
    );
  }

  Widget _buildItem(int index) {
    var borderWidth = 18.w;
    bool loading = carts[index].loading;

    if (loading) {
      return Container(
        // color: const Color.fromARGB(104, 158, 158, 158),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 120.w,
              height: 120.w,
              child: CircularProgressIndicator(
                valueColor:
                    const AlwaysStoppedAnimation<Color>(BTN_PRIMARY_COLOR),
                backgroundColor: Colors.grey[200],
                strokeWidth: 8.w,
              ),
            ),
            // SizedBox(height: 20.w,),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.center,
            //   children: [
            //     Text("$cartsShowLength", style: TextStyle(color: Colors.white, fontSize: 30.w),),
            //     Text("/", style: TextStyle(color: Colors.white, fontSize: 30.w),),
            //     Text("${carts.length}", style: TextStyle(color: Colors.white, fontSize: 30.w),),
            //   ],
            // )
          ],
        ),
      );
    }

    return FadeInContainer(
        key: Key('item_${index.toString()}'),
        child: GestureDetector(
            onTap: () {
              setState(() {
                carts[index].products[0].quantity =
                    carts[index].products[0].quantity > 0 ? 0 : 1;
                carts = [...carts];

                _updateTotals();

                floatingCountdownKey.currentState?.reset();
              });
            },
            child: Stack(
              children: [
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  padding: EdgeInsets.all(borderWidth),
                  decoration: BoxDecoration(
                    color: carts[index].products[0].quantity > 0
                        ? const Color(0xFF92E61E)
                        : const Color(0xFF383838),
                    borderRadius: BorderRadius.circular(10.w),
                  ),
                  child: Container(
                    child: ClipRRect(
                      // borderRadius: BorderRadius.circular(10.w),
                      child: Image.file(
                        File(carts[index].path),
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                if (carts[index].products[0].quantity > 0)
                  Positioned(
                      right: 20.w,
                      top: 20.w,
                      child: SizedBox(
                          width: 66.w,
                          height: 66.w,
                          child: Lottie.asset(
                              'assets/images/animate-radio.json',
                              repeat: false,
                              fit: BoxFit.contain))),
              ],
            )));
  }

  _updateTotals() {
    var obj = _calcAmount();
    setState(() {
      cardsCount = obj['total'];
      paymentTotal = obj['paymentAmount'];
      originalAmount = obj['originalAmount'];
      paymentDiscount = obj['originalAmount'] - obj['paymentAmount'];
    });
  }

  String removePrefix(String path, String prefix) {
    if (path.startsWith(prefix)) {
      return path.replaceFirst(prefix, '');
    }
    return path;
  }

  (Decimal, Decimal) _calcGroupAmount(
      dynamic goodsList, DiscountModel? discountModel) {
    Decimal originalAmount = Decimal.zero;
    for (var item in goodsList) {
      originalAmount += item['goodsPrice'] * Decimal.fromInt(item['goodsNum']);
    }

    Decimal paymentAmount = originalAmount;
    if (discountModel != null) {
      paymentAmount =
          (originalAmount * discountModel.discount / Decimal.fromInt(10))
              .toDecimal(scaleOnInfinitePrecision: 5);
    }
    return (originalAmount, paymentAmount);
  }

  dynamic _calcAmount() {
    var list = [];
    var goodsGroupList = {};

    int total1 = 0;
    for (var item in carts) {
      List<ProductModel> products = item.products;
      for (ProductModel product in products) {
        if (product.quantity > 0) {
          // originalAmount += Decimal.fromInt(product.quantity) * product.price;
          total1 += product.quantity;
          var one = {
            'goodsId': product.id,
            'goodsNum': product.quantity,
            'goodsType': product.type,
            'basePicUUID': item.uuid,
            'picUrl': Platform.isAndroid
                ? Uri.parse(item.path).path
                : removePrefix(item.path, '/data/files/'),
            'goodsPrice': product.price,
          };
          list.add(one);
          if (!goodsGroupList.containsKey(product.type)) {
            goodsGroupList[product.type] = [];
          }
          goodsGroupList[product.type].add(one);
        }
      }
    }

    Decimal originalAmount = Decimal.zero;
    Decimal paymentAmount = Decimal.zero;
    int discountId = -1;
    DiscountModel? discount = discounts.firstOrNull;
    if (discount == null) {
      (originalAmount, paymentAmount) = _calcGroupAmount(list, discount);
    } else if (discount.type == 'all' || discount.type == '') {
      DiscountModel? discount = discounts
          .where((item) =>
              item.piece <= total1 && (item.type == 'all' || item.type == ''))
          .firstOrNull;
      (originalAmount, paymentAmount) = _calcGroupAmount(list, discount);
      discountId = discount == null ? -1 : discount.id;
    } else {
      for (var typeKey in goodsGroupList.keys) {
        var oneGroup = goodsGroupList[typeKey];
        var oneGroupTotal =
            oneGroup.fold(0, (sum, item) => sum + item['goodsNum']);
        DiscountModel? discount = discounts
            .where((item) => item.type == typeKey)
            .where((item) => item.piece <= oneGroupTotal)
            .firstOrNull;
        var (groupOriginalAmount, groupPaymentAmount) =
            _calcGroupAmount(oneGroup, discount);
        originalAmount += groupOriginalAmount;
        paymentAmount += groupPaymentAmount.ceil(scale: 2);
      }
      discountId = 0;
    }

    int scale =
        appConfig.currencyType != null && appConfig.currencyType == 'JPY'
            ? 0
            : 2;

    return {
      "total": total1,
      "discountId": discountId == -1 ? null : discountId,
      "originalAmount": originalAmount.round(scale: scale),
      "paymentAmount": paymentAmount.ceil(scale: scale), // 上舍入
      "list": list
    };
  }

  List<DiscountModel> _filterDiscountsNow() {
    DiscountModel? discount = discounts.firstOrNull;
    if (discount == null) {
      return [];
    }
    if (discount.type == 'all' || discount.type == '') {
      return discounts
          .where((item) => (item.type == 'all' || item.type == ''))
          .toList();
    }
    return discounts
        .where((item) => !(item.type == 'all' || item.type == ''))
        .toList();
  }

  create() async {
    if (paymentTotal.toDouble() < 0.01) {
      Toast.error(context, '请选择数量');
      return;
    }

    if (isLoading) return;
    setState(() {
      isLoading = true;
    });

    var obj = _calcAmount();

    var params = {
      'screenNumber': Global.instance.screenNumber,
      'businessData': obj['list'],
      'paymentAmount': obj['paymentAmount'],
      'originalAmount': obj['originalAmount'],
      'discountId': obj['discountId'],
      'originalityPicUrl': Global.instance.orignalPath,
    };

    if (Platform.isAndroid && Global.instance.deviceCode.isNotEmpty) {
      params['padNo'] = Global.instance.deviceCode;
    }

    try {
      var response = await ApiService().createOrder(params);
      if (response['code'] != '100') {
        return;
      }
      Global.instance.orderInfo = response['data'];
      Global.instance.orderInfo["paymentAmount"] = obj['paymentAmount'];
      Global.instance.orderInfo["originalAmount"] = obj['originalAmount'];
    } catch (e) {
      return;
    } finally {
      setState(() {
        isLoading = false;
      });
    }

    Navigator.push(
      // ignore: use_build_context_synchronously
      context,
      MaterialPageRoute(builder: (context) => const PagePayment()),
    );
  }

  Widget _buildCheckout() {
    const textColor = Colors.white;
    const textNumColor = Colors.white;
    const bgColor = Color(0xFFEE3342);

    return Stack(children: [
      Container(
          padding: EdgeInsets.only(top: 35.w, bottom: 50.w),
          decoration: BoxDecoration(
            color: Colors.black38,
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30.w),
                topRight: Radius.circular(30.w)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                    discounts.length,
                    (i) => Text(
                          "${discounts[i].name}${i + 1 < discounts.length ? '、 ' : ''}",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 40.w,
                              fontWeight: FontWeight.bold),
                        )).toList(),
              ),
              SizedBox(
                height: 35.w,
              ),
              Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        // Navigator.pop(context);
                        backToHome(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey,
                        foregroundColor: Colors.black54,
                        padding: EdgeInsets.fromLTRB(
                          120.w,
                          30.w,
                          120.w,
                          30.w,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(120.w),
                        ),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "返回",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 45.w,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 10.w),
                            ),
                            Text(
                              "Back",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 21.w,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),

                    SizedBox(width: 60.w),

                    // Confirm button
                    InkWell(
                        onTap: isLoading || cardsCount <= 0
                            ? null
                            : () async {
                                create();
                              },
                        child: Container(
                          width: 550.w,
                          height: 120.w,
                          decoration: const BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/images/pay-btn-bg.png'),
                              fit: BoxFit.fitHeight,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 370.w,
                                padding: EdgeInsets.only(left: 80.w),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (paymentDiscount > Decimal.zero)
                                      Text(
                                        "￥${originalAmount}原价",
                                        style: TextStyle(
                                          fontFamily: 'DouyinSansBold',
                                          color: Colors.black45,
                                          fontSize: 25.w,
                                          decoration:
                                              TextDecoration.lineThrough,
                                          decorationColor: Colors.black45,
                                        ),
                                      ).zoomIn(
                                          duration: const Duration(
                                              milliseconds: 300)),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.baseline,
                                      textBaseline: TextBaseline.alphabetic,
                                      children: [
                                        AnimatedDefaultTextStyle(
                                          duration:
                                              const Duration(milliseconds: 300),
                                          style: TextStyle(
                                              fontFamily: 'DouyinSansBold',
                                              color: Colors.black,
                                              fontSize:
                                                  paymentDiscount > Decimal.zero
                                                      ? 60.w
                                                      : 70.w,
                                              fontWeight: FontWeight.bold,
                                              height: 1),
                                          child: Text(
                                            "${paymentTotal}",
                                          ),
                                        ),
                                        Stack(
                                          clipBehavior: Clip.none,
                                          children: [
                                            AnimatedDefaultTextStyle(
                                                duration: const Duration(
                                                    milliseconds: 300),
                                                style: TextStyle(
                                                    fontFamily:
                                                        'DouyinSansBold',
                                                    color: Colors.black,
                                                    fontSize: paymentDiscount >
                                                            Decimal.zero
                                                        ? 30.w
                                                        : 40.w,
                                                    letterSpacing: 20.w,
                                                    fontWeight: FontWeight.bold,
                                                    height: 1),
                                                child: const Text(
                                                  "￥",
                                                )),
                                            if (paymentDiscount > Decimal.zero)
                                              Positioned(
                                                  top: -50.w,
                                                  left: -10.w,
                                                  child: Image.asset(
                                                    "assets/images/pay-btn-sign.png",
                                                    width: 130.w,
                                                  ).fadeIn(
                                                      duration: const Duration(
                                                          milliseconds: 300)))
                                          ],
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                alignment: Alignment.center,
                                padding: EdgeInsets.only(left: 10.w),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "支付",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 45.w,
                                        letterSpacing: 10.w,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      "Pay",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 21.w,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        )),
                  ]),
            ],
          )),
    ]);
  }

  Widget _buildBack() {
    return const ButtonBack();
  }

  Widget _buildLeaderTip() {
    TextStyle baseStyle = TextStyle(
        color: Colors.white,
        fontSize: 50.w,
        fontWeight: FontWeight.bold,
        height: 1);

    TextStyle smallStyle = TextStyle(
      fontSize: 24.w,
    );
    TextStyle colorHighlight = const TextStyle(
      color: Color(0xFF92E61E),
    );

    return Container(
      height: 210.w,
      padding: EdgeInsets.fromLTRB(16.w, 16.w, 165.w, 16.w),
      margin: EdgeInsets.all(30.w),
      child: Row(
        children: [
          const WidgetWave(),
          Expanded(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: cartsShowLength == 0
                  ? RichText(
                      key: const ValueKey(1),
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: baseStyle,
                        children: <TextSpan>[
                          TextSpan(text: '魔法正在酝酿中～\n'),
                          TextSpan(
                              text: '✨ ',
                              style: TextStyle(color: Colors.yellow)
                                  .merge(smallStyle)),
                          TextSpan(
                              text: 'Magic is brewing...\n\n',
                              style: smallStyle),
                          TextSpan(text: '咔哒照就要来了！\n'),
                          TextSpan(
                              text: 'Your amazing photo is on the way!',
                              style: smallStyle),
                        ],
                      ),
                    )
                  : RichText(
                      key: const ValueKey(2),
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: baseStyle,
                        children: <TextSpan>[
                          TextSpan(text: '上下滑动', style: colorHighlight),
                          TextSpan(text: '查看照片\n'),
                          TextSpan(
                              text: 'Swipe up and down ',
                              style: smallStyle.merge(colorHighlight)),
                          TextSpan(
                              text: 'to browse photos\n\n', style: smallStyle),
                          TextSpan(text: '点击选择心仪款式\n'),
                          TextSpan(
                              text: 'Tap to select your favorite style',
                              style: smallStyle),
                        ],
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  void startRequest() {
    var list = [];
    List<CartModel> cartsNow = [];
    Map<String, CartModel> finishedMap = HashMap();
    for (var cart in carts) {
      if (cart.loading) continue;
      finishedMap[cart.uuid] = cart;
    }

    for (var item in styleModel.picCollect) {
      List<ProductModel> productsClone =
          products.map((item) => ProductModel.fromJson(item.toJson())).toList();

      if (finishedMap.containsKey(item.uuid)) {
        productsClone[0].quantity =
            finishedMap[item.uuid]!.products[0].quantity;
        var cart = CartModel(
            path: finishedMap[item.uuid]!.path,
            uuid: item.uuid,
            loading: false,
            products: productsClone);
        cartsNow.add(cart);
        continue;
      } else {
        var cart = CartModel(
            path: '', uuid: item.uuid, loading: true, products: productsClone);
        cartsNow.add(cart);
      }

      if (Platform.isLinux) {
        list.add({
          'index': list.length,
          'params': {
            'imagePath': '${Global.instance.filePath}/${avatarPath}',
            'templates': [
              {
                'uuid': item.uuid,
                'path': '${Global.instance.filePath}/style/${item.uuid}.jpg'
              }
            ]
          }
        });
      } else {
        list.add({
          'index': list.length,
          'params': {
            'screen_num': Global.instance.deviceCode.isNotEmpty
                ? Global.instance.deviceCode
                : Global.instance.screenNumber,
            'flow_uuid': Global.instance.flowUuid,
            'imageBase64': Global.instance.originalBase64,
            'templates': [
              {'uuid': item.uuid, 'path': '/data/files/style/${item.uuid}.jpg'}
            ]
          }
        });
      }
    }

    setState(() {
      carts = cartsNow;
    });

    _requestPool = NetworkRequestPool(
      maxConcurrent: 1,
      requestIndices: list,
    );

    _requestPool.results.listen((result) {
      if (!mounted) {
        return;
      }

      if (result.path == null) {
        Toast.error(
            context, Platform.isAndroid ? result.error ?? '未检测到人脸' : '未检测到人脸');

        backToHome(context);
        return;
      }

      int index = cartsNow.indexWhere((item) => item.uuid == result.uuid);
      cartsNow[index].path = result.path!;
      cartsNow[index].loading = false;

      setState(() {
        carts = [...cartsNow];
      });

      if (!soundedSelect) {
        soundedSelect = true;
        SoundPlayer().play('cartSelect');
      }
    });

    _requestPool.start();
  }
}

class FadeInContainer extends StatefulWidget {
  final Widget child;

  const FadeInContainer({
    super.key,
    required this.child,
  });

  @override
  _FadeInContainerState createState() => _FadeInContainerState();
}

class _FadeInContainerState extends State<FadeInContainer>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<Offset> _offsetAnimation;

  var offset = -20.w;
  var duration = const Duration(milliseconds: 800);

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: duration,
      vsync: this,
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );

    _offsetAnimation = Tween<Offset>(
      begin: Offset(0, offset / 100),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.translate(
            offset: Offset(0, -_offsetAnimation.value.dy * 100), // 转换回像素值
            child: child,
          ),
        );
      },
      child: widget.child,
    );
  }
}
