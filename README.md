# kadaya


## flutter_webrtc
```
// fix了摄像头拔出死循环
// 优化了captureFrame使用文件传递内容的性能问题
plugin/flutter_webrtc/common/cpp/include/flutter_frame_capturer.h
plugin/flutter_webrtc/common/cpp/src/flutter_frame_capturer.cc

// 增加两个导出
plugin/flutter_webrtc/lib/flutter_webrtc.dart 
// captureFrame 文件传递改为bytebuffer
plugin/flutter_webrtc/lib/src/native/media_stream_track_impl.dart
```


index   index.wav

genderSelect  gender-select.wav

captureTip 
captureBegin  capture.wav
captureDone   capture-done.wav

cartWait           cart-wait.wav
cartSelect??  cart-select.wav

paymentPaySuccess   payment-payed.wav
paymentPrintBegin   payment-printed.wav
paymentPicQrCode    payment-qrcode.wav

