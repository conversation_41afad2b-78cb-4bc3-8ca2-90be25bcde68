import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

class FileSaver {
  // 写入 JSON 文件
  static Future<void> writeJsonFile(
      Map<String, dynamic> data, String filePath) async {
    final file = File(filePath);

    // 如果目录不存在，则创建目录
    if (!await file.parent.exists()) {
      await file.parent.create(recursive: true);
    }

    // 将数据转换为 JSON 字符串并写入文件
    final jsonString = jsonEncode(data);
    await file.writeAsString(jsonString);
  }

// 读取 JSON 文件
  static Future<Map<String, dynamic>> readJsonFile(String filePath) async {
    final file = File(filePath);

    if (await file.exists()) {
      // 读取文件内容并解析为 JSON
      final jsonString = await file.readAsString();
      return jsonDecode(jsonString);
    } else {
      Directory directory = file.parent;
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      return {};
    }
  }

  static String getFileNameWithoutExtension(String url) {
    // 使用 Uri 解析 URL
    Uri uri = Uri.parse(url);

    // 获取路径部分
    String path = uri.path;

    // 使用 split 方法获取文件名
    List<String> segments = path.split('/');
    String fileNameWithExtension = segments.last;

    // 去掉文件后缀
    List<String> fileNameParts = fileNameWithExtension.split('.');
    if (fileNameParts.length > 1) {
      fileNameParts.removeLast(); // 移除后缀部分
    }

    // 重新组合为文件名（不包含后缀）
    return fileNameParts.join('.');
  }

  /// 检查文件是否存在
  static Future<bool> _fileExists(String filename) async {
    String filePath = '/data/files/$filename';
    if (Platform.isAndroid) {
      final directory = await getApplicationDocumentsDirectory();
      filePath = '${directory.path}/$filename';
    }
    final file = File(filePath);

// 检查目录是否存在，如果不存在则创建
    Directory directory = file.parent;
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    return file.exists();
  }

  static delFiles(String path) async {
    String basePath = '/data/files/$path';
    if (Platform.isAndroid) {
      final directory = await getApplicationDocumentsDirectory();
      basePath = '${directory.path}/$path';
    }
// 获取今天的日期
    DateTime today = DateTime.now();

    // 计算两天前的日期
    DateTime twoDaysAgo = today.subtract(Duration(days: 2));

    // 格式化日期为 yyyyMMdd
    DateFormat formatter = DateFormat('yyyyMMdd');
    String twoDaysAgoFormatted = formatter.format(twoDaysAgo);

    try {
      // 获取目录下的所有文件夹
      Directory baseDirectory = Directory(basePath);
      List<FileSystemEntity> entities = baseDirectory.listSync();

      for (FileSystemEntity entity in entities) {
        if (entity is Directory) {
          // 获取文件夹名称
          String folderName = entity.path.split('/').last;

          // 检查文件夹名称是否符合日期格式
          if (folderName.length == 8 && int.tryParse(folderName) != null) {
            // 将文件夹名称转换为日期
            // DateTime folderDate = formatter.parseStrict(folderName);
            DateTime folderDate = DateTime(
                int.parse(folderName.substring(0, 4)),
                int.parse(folderName.substring(4, 6)),
                int.parse(folderName.substring(6, 8)));

            // 如果文件夹日期早于两天前，则删除该文件夹
            if (folderDate.isBefore(twoDaysAgo)) {
              entity.deleteSync(recursive: true);
              print('Deleted folder: ${entity.path}');
            }
          }
        }
      }
    } catch (e) {
      print('error $e');
    }
  }

  /// 保存文件
  /// [data] 可以是网络 URL（String）或 Uint8List
  /// [fileName] 是保存的文件名（包括扩展名，如 "image.jpg"）
  /// 返回 true 表示成功，false 表示失败
  static Future<bool> saveFile({
    required dynamic data,
    required String fileName,
  }) async {
    try {
      // 检查文件是否已经存在
      if (await _fileExists(fileName)) {
        print('fileExist ' + fileName);
        return true; // 文件已存在，直接返回成功
      } else {
        print('download ' + fileName);
      }

      Uint8List fileData;

      if (data is String) {
        final response = await http
            .get(Uri.parse(data))
            .timeout(const Duration(seconds: 50));
        if (response.statusCode != 200) {
          debugPrint('download ${data} code:${response.statusCode}');
          return false;
        }
        fileData = response.bodyBytes;
        if (fileData.isEmpty) {
          debugPrint('file ${data} is empty');
          return false;
        }
      } else if (data is Uint8List) {
        // 如果 data 是 Uint8List，直接使用
        fileData = data;
      } else {
        throw ArgumentError('saveFile() data Unsupported data type');
      }

      // 获取保存路径
      String filePath = '/data/files/$fileName';
      if (Platform.isAndroid) {
        final directory = await getApplicationDocumentsDirectory();
        filePath = '${directory.path}/$fileName';
      }

      // 保存文件
      final file = File(filePath);
      await file.writeAsBytes(fileData);

      debugPrint('File saved to: $filePath');
      return true;
    } catch (e) {
      debugPrint('Error saving file: $e');
      return false;
    }
  }

  /// 批量保存文件，限制最大线程数为 CPU 核数
  /// [files] 是一个包含 FileObject 的数组
  /// 返回一个 Map，包含成功和失败的数量
  ///
  static Stream<Map<String, dynamic>> saveFilesConcurrently({
    required List files,
  }) async* {
    int successCount = 0;
    int total = files.length;
    for (int i = 0; i < total; i++) {
      final file = files[i];
      final url = file['url']!;
      final fileName = file['fileName']!;
      // final uuid = file['uuid']!;
      // final subDir = file['subDir'] ?? 'style';
      // final ext = Uri.parse(url).path.split('.').last;
      // String fileName = '$subDir/$uuid.$ext';
      // if (subDir == 'style') {
      //   fileName = 'style/$uuid.jpg';
      // } else {
      //   fileName = '$subDir/$uuid.$ext';
      // }
      final result = await saveFile(data: url, fileName: fileName);
      successCount += result ? 1 : 0;

      yield {
        'index': i + 1,
        'total': total,
        'result': result,
      };
    }

    int failureCount = files.length - successCount;

    yield {
      'success': successCount,
      'failure': failureCount,
    };
  }

  static Future<void> saveAssetImageToLocal() async {
    try {
      // final executableDir = await getApplicationExecutableDirectory();
      // final fullPath = '$executableDir/assets/images/demo.jpg';

      final String targetDirPath = '/data/flutter';
      final Directory targetDir = Directory(targetDirPath);

      // 尝试创建目录（可能需要sudo权限）
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      // 3. 从assets加载文件
      final ByteData byteData = await rootBundle.load('assets/images/demo.jpg');
      final List<int> imageBytes = byteData.buffer.asUint8List();

      // 4. 写入文件
      // final String filePath = path.join(targetDirPath, 'demo.jpg');
      final File file = File('/data/flutter/demo.jpg');
      await file.writeAsBytes(imageBytes);
    } catch (e) {}
  }

  static Future<String> getApplicationExecutableDirectory() async {
    if (kReleaseMode) {
      //final appDocDir = await getApplicationExecutableDirectory();
      final exePath = Platform.resolvedExecutable; // 完整可执行文件路径
      final exeDir = File(exePath).parent.path; // 所在目录
      return '${exeDir}/data/flutter_assets';
    }
    // 获取应用可执行文件所在目录
    final script = Platform.script.toFilePath();
    return Directory(script).parent.path;
  }
}
