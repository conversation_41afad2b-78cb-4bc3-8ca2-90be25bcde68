// lib/services/storage_service.dart
import 'dart:convert';

import 'package:kadaya/models/style_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  late final SharedPreferences _prefs;

  factory StorageService() {
    return _instance;
  }

  StorageService._internal();

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  Future<void> saveStyles(List<StyleModel> styles) async {
    final String jsonString = json.encode(
      styles.map((style) => style.toJson()).toList(),
    );
    await _prefs.setString('styles', jsonString);
  }

  Future<List<StyleModel>> getStyles() async {
    final String? jsonString = _prefs.getString('styles');
    if (jsonString == null) return [];
    
    final List<dynamic> jsonList = json.decode(jsonString);
    return jsonList.map((json) => StyleModel.fromJson(json)).toList();
  }
}