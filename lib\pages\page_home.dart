// lib/pages/home_page.dart
import 'dart:async';
import 'dart:collection';
import 'dart:io';
import 'dart:ui';

import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kadaya/pages/page_gender.dart';
import 'package:kadaya/pages/page_splash.dart';
import 'package:kadaya/utils/constants.dart';
import 'package:kadaya/utils/global.dart';
import 'package:kadaya/utils/sound_player.dart';
import 'package:kadaya/utils/util.dart';
import 'package:kadaya/widgets/config_dialog.dart';
import 'package:path_provider/path_provider.dart';

import '../utils/file_saver.dart';

class PageHome extends StatefulWidget {
  PageHome({super.key});

  @override
  State<PageHome> createState() => _PageHomeState();
}

class _PageHomeState extends State<PageHome> with RouteAware {
  // swipper
  int _currentIndex = 0;
  late List<String> carouselImages;
  late Map<String, Widget> cacheWidgets;
  Timer? _soundTimer;
  double? _soundSeconds;

  // router
  var routeObserver = Global.instance.routeObserver;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void didPopNext() {
    // became visible again
    _didIn();
  }

  @override
  void didPushNext() {
    // new route over me
    _didOut();
  }

  void _didOut() {
    _soundTimer?.cancel();
  }

  void _didIn() async {
    _soundSeconds ??= await SoundPlayer().getWavSeconds("index");
    _soundSeconds ??= 20;
    SoundPlayer().play("index");
    _soundTimer =
        Timer.periodic(Duration(seconds: _soundSeconds!.ceil() + 6), (timer) {
      SoundPlayer().play("index");
    });
  }

  @override
  void initState() {
    super.initState();
    cacheWidgets = HashMap();
    carouselImages = Global.instance.appConfig.carouselImages;
    _didIn();
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _didOut();
    super.dispose();
  }

  void showSetting() async {
    final path = await _configFilePath;
    showDialog(
        barrierDismissible: false,
        context: context,
        builder: (context) => ConfigDialog(
              onDismiss: (isSaved, data) async {
                if (data != null) {
                  String yaml = mapToYaml(data);
                  try {
                    final file = File(path);
                    var result = await file.writeAsString(yaml);
                  } catch (e) {
                    print('Error writing default config: $e');
                  }
                  print('Dialog data: $data');

                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => PageSplash(),
                    ),
                  );
                } else {}
              },
              defaultData: Global.instance.nowConfigMap(),
            ));
  }

  // 获取配置文件路径
  static Future<String> get _configFilePath async {
    String configFileName = 'config.yaml';
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/$configFileName';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          if (Platform.isAndroid)
            Positioned(
              right: 30.w,
              top: 30.w,
              child: GestureDetector(
                onTap: showSetting,
                child: Text(
                  '设置',
                  style: TextStyle(color: Colors.white, fontSize: 25.sp),
                ),
              ),
            ),
          Image.file(
            File(_swiperFileByIndex(_currentIndex)),
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          ),
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 30.w, sigmaY: 30.w),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withAlpha((255.0 * 0.5).round()),
                    Colors.black.withAlpha((255.0 * 0.1).round()),
                  ],
                ),
              ),
            ),
          ),
          Column(
              mainAxisSize: MainAxisSize.max,
              // mainAxisAlignment: MainAxisAlignment.spaceAround,
              // spacing: 50.w,
              children: [
                // header
                _buildHeader(),

                SizedBox(
                  height: 100.w,
                ),

                // swiper
                _buildSwiper(),

                // button
                SizedBox(
                  height: 360.w,
                  child: Center(
                    child: _buildButton(),
                  ),
                )
              ]),
          Positioned(
              left: 20.w,
              bottom: 10.w,
              right: 20.w,
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "sn:${Global.instance.screenNumber}",
                    style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white.withAlpha((255.0 * 0.5).round())),
                  ),
                  SizedBox(
                    width: 20.w,
                  ),
                  Text(
                    "v:${Global.instance.appVersion}",
                    style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white.withAlpha((255.0 * 0.5).round())),
                  )
                ],
              )),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 100.w,
        ),
        Text(
          "AI一键生成旅拍大片",
          style: TextStyle(
            color: Colors.white,
            fontSize: 80.w,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Color.fromARGB(136, 42, 58, 20),
                blurRadius: 5.w,
                offset: Offset(3.w, 3.w),
              ),
            ],
          ),
        ),
        Text(
          "Turn Your Trips into Cinematic Magic with AI",
          style: TextStyle(
            color: Colors.white,
            fontSize: 30.w,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Color.fromARGB(136, 42, 53, 26),
                blurRadius: 4.w,
                offset: Offset(3.w, 3.w),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildButton() {
    return Container(
      width: 1.sw - 300.w,
      height: 130.w,
      decoration: BoxDecoration(
        color: BTN_PRIMARY_COLOR,
        borderRadius: BorderRadius.circular(75.w),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(75.w),
          onTap: () async {
            await Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => PageGender()),
            );
          },
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "点我拍照",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 64.w,
                          fontWeight: FontWeight.bold),
                    ),
                    Text(
                      "Take Photo",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 24.w,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _swiperFileByIndex(int index) {
    return '${Global.instance.filePath}/style/${FileSaver.getFileNameWithoutExtension(carouselImages[index])}.jpg';
  }

  Widget _buildSwiper() {
    int showCount = 6;
    var beginOffset = -100.w;

    return Swiper(
      onIndexChanged: (value) => setState(() {
        _currentIndex = value;
      }),
      layout: SwiperLayout.CUSTOM,
      customLayoutOption: CustomLayoutOption(
          startIndex: -1, stateCount: showCount)
        ..addOpacity([
          0.5,
          ...List.generate(showCount - 2, (index) => 1 - index * 0.2),
          0
        ])
        ..addScale(
            [1, ...List.generate(showCount - 1, (index) => 1 - index * 0.02)],
            const Alignment(0, 0))
        ..addTranslate([
          Offset(-1.sw, 0),
          ...List.generate(
              showCount - 1, (index) => Offset(beginOffset + index * 80.w, 0.0))
        ]),
      itemBuilder: (BuildContext context, int index) {
        return swiperItemCache(index);
      },
      axisDirection: AxisDirection.right,
      itemCount: carouselImages.length,
      itemWidth: 0.68.sw,
      itemHeight: 0.68.sw * 3 / 2,
      autoplay: true,
      autoplayDelay: 5000,
      duration: 500,
      // controller: _controller,
    );
  }

  Widget swiperItemCache(int index) {
    var key = "swiper_item_${index.toString()}";
    if (!cacheWidgets.containsKey(key)) {
      cacheWidgets[key] = _swiperItemCache(key, index);
    }
    return cacheWidgets[key]!;
  }

  Widget _swiperItemCache(String key, index) {
    return Container(
        key: ValueKey(key),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.w),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha((255.0 * 0.3).round()),
              blurRadius: 5.w,
              offset: Offset(3.w, 3.w),
            ),
          ],
        ),
        child: ClipRRect(
            borderRadius: BorderRadius.circular(15.w),
            child: Image.file(
              File(_swiperFileByIndex(index)),
              fit: BoxFit.cover,
            )));
  }
}
