import 'package:flutter/material.dart';
import 'package:kadaya/models/cart_model.dart';
import 'package:kadaya/models/config_model.dart';
import 'package:kadaya/models/discount_model.dart';
import 'package:kadaya/models/product_model.dart';
import 'package:kadaya/models/style_model.dart';
import 'package:kadaya/services/webrtc_service.dart';

import '../pages/page_home.dart';

const APP_VERSION =
    String.fromEnvironment('APP_VERSION', defaultValue: 'dev-0.0.1');
final ROUTER_OBSERVER = RouteObserver<PageRoute>();

class Global {
  Global._privateConstructor();

  static final Global _instance = Global._privateConstructor();

  static Global get instance => _instance;

  static late WebRTCService webRTCService;

  bool isRunPc = false; // 普通pc上运行

  String screenNumber = '';
  String deviceCode = '';

  String filePath = '';

  //
  String originalBase64 = '';
  String orignalPath = '';
  String flowUuid = '';

  Map<String, String> defaultSounds = {
    "index": "assets/audios/index.wav",
    "genderSelect": "assets/audios/gender-select.wav",
    // "captureTip" : "",
    "captureBegin": "assets/audios/capture-begin.wav",
    "captureDone": "assets/audios/capture-done.wav",
    "generateBegin": "assets/audios/generate-begin.wav",
    "cartSelect": "assets/audios/cart-select.wav",
    "paymentQrcode": "assets/audios/payment-qrcode.wav",
    "paymentPayed": "assets/audios/payment-payed.wav",
    "paymentPrinted": "assets/audios/payment-printed.wav",
  };
  Map<String, String> nowSounds = {};
  List<StyleModel> styleModels = [];
  StyleModel? currentStyleModel;
  List<DiscountModel> discounts = [];
  List<ProductModel> products = [];

  List<CartModel> carts = [];
  List<Map<String, dynamic>> styles = [];

  Map ossConfig = {};
  late AppConfig appConfig;

  late Map orderInfo;

  int indexSwiper = 0;
  List allProducts = [];

  int cameraDirection = 1;

  bool isFirst = true;
  bool isOnline = true;

  String appVersion = APP_VERSION;
  RouteObserver<PageRoute> routeObserver = ROUTER_OBSERVER;

  Map<String, String> nowConfigMap() {
    return {
      "env": isOnline ? 'prod' : 'dev',
      'screenNum': screenNumber,
      'deviceCode': deviceCode,
    };
  }

  mockStepConfig() {
    instance.isOnline = false;
    instance.isRunPc = true;
    instance.screenNumber = "001";
  }

  mockStepPayment() {
    mockStepConfig();
    instance.orderInfo = {
      "id": 698,
      "agentId": 1,
      "merchantId": 3,
      "storeId": 3,
      "storeName": "天翔睿翼测试机",
      "screenId": 10,
      "screenNum": "ai-pc",
      "paymentChannelId": 1,
      "businessOrderId": "8a87205ffec24b5c",
      "businessData": [
        {
          "goodsId": 214,
          "goodsNum": 1,
          "discountId": null,
          "goodsType": "postcards",
          "goodsTypeName": null,
          "basePicUUID": "axOQFkQNd9DA2KH",
          "picUrl": "screen/20250507/ai-pc/1746607305054/axOQFkQNd9DA2KH.jpg",
          "printTaskUUID": "f899d4e9-e011-4b97-8a72-4a59b1ac109b",
          "printerNo": null
        }
      ],
      "payUrl": "xxxx",
      "phone": null,
      "platformUid": "oGFfksywaxitOwNYQsT1gOe7SiHE",
      "paymentAmount": 0.01,
      "originalAmount": 0.10,
      "discountId": 109,
      "payTime": "2025-05-07T16:45:16.000+08:00",
      "payStatus": 1,
      "paymentOrderNo": "4200002697202505074026535120",
      "originalityPicUrl": "screen/20250507/ai-pc/1746607305054/original.jpg",
      "orderSource": "1",
      "deleteTime": null,
      "createTime": "2025-05-07T16:43:47.000+08:00",
      "updateTime": "2025-05-07T16:45:16.000+08:00",
      "agent": {"id": 1, "name": "代理商1"},
      "merchant": {"id": 3, "name": "商户3"},
      "store": {"id": 3, "name": "天翔睿翼测试机"},
      "screen": {"id": 10, "number": "ai-pc"}
    };
  }
}

void backToHome(BuildContext context) {
  Navigator.pushAndRemoveUntil(
    context,
    MaterialPageRoute(builder: (context) => PageHome()),
    (route) => false,
  );
}
