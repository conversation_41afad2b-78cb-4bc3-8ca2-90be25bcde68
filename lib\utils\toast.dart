import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:toastification/toastification.dart';

class Toast {
  static ToastificationItem _show(
      BuildContext? context, msg, ToastificationType type,
      {int seconds = 5}) {
    var color = Colors.lightBlue;
    var icon = Icon(Icons.info_outline, color: color, size: 32.w);
    switch (type) {
      case ToastificationType.error:
        color = Colors.red;
        icon = Icon(Icons.error, color: color, size: 32.w);
        break;
      case ToastificationType.success:
        color = Colors.green;
        icon = Icon(Icons.check_circle_outline, color: color, size: 32.w);
        break;
      case ToastificationType.info:
        color = Colors.lightBlue;
        icon = Icon(Icons.info_outline, color: color, size: 32.w);
        break;
      case ToastificationType.warning:
        color = Colors.amber;
        icon = Icon(Icons.warning, color: color, size: 32.w);
        break;
    }

    var item = toastification.showCustom(
      context: context,
      alignment: Alignment.topRight,
      animationDuration: const Duration(milliseconds: 500),
      autoCloseDuration: Duration(seconds: seconds),
      builder: (context, item) {
        return Container(
          margin: EdgeInsets.only(right: 25.w, top: 20.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.w),
            border: Border.all(color: color, width: 1.w),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(60),
                blurRadius: 10.w,
                offset: Offset(0, 10.w),
              ),
            ],
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.w),
          child: Row(
            children: [
              icon,
              SizedBox(width: 16.w),
              Expanded(
                child: Text(
                  msg,
                  style: TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                    fontSize: 21.sp,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 16.w),
              GestureDetector(
                onTap: () => toastification.dismiss(item),
                child: Icon(Icons.close, color: Colors.black45, size: 26.w),
              ),
            ],
          ),
        );
      },
    );
    return item;
  }

  static ToastificationItem error(BuildContext? context, msg,
      {int seconds = 5}) {
    return _show(context, msg, ToastificationType.error, seconds: seconds);
  }

  static ToastificationItem success(BuildContext? context, msg,
      {int seconds = 5}) {
    return _show(context, msg, ToastificationType.success, seconds: seconds);
  }

  static ToastificationItem info(BuildContext? context, msg,
      {int seconds = 5}) {
    return _show(context, msg, ToastificationType.info, seconds: seconds);
  }

  static ToastificationItem warning(BuildContext? context, msg,
      {int seconds = 5}) {
    return _show(context, msg, ToastificationType.warning, seconds: seconds);
  }
}
