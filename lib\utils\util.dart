String mapToYaml(Map<String, dynamic> map, [int indent = 0]) {
  final buffer = StringBuffer();
  final indentStr = '  ' * indent;

  map.forEach((key, value) {
    if (value is Map<String, dynamic>) {
      buffer.writeln('$indentStr$key:');
      buffer.write(mapToYaml(value, indent + 1));
    } else {
      buffer.writeln('$indentStr$key: "${value.toString()}"');
    }
  });

  return buffer.toString();
}
