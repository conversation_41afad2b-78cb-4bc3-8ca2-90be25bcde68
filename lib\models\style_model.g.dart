// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'style_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StyleModel _$StyleModelFromJson(Map<String, dynamic> json) => StyleModel(
      id: (json['id'] as num).toInt(),
      agentId: (json['agentId'] as num?)?.toInt(),
      merchantId: (json['merchantId'] as num?)?.toInt(),
      storeId: (json['storeId'] as num?)?.toInt(),
      styleNum: (json['styleNum'] as num).toInt(),
      styleName: json['styleName'] as String,
      sex: (json['sex'] as num).toInt(),
      version: json['version'] as String,
      updateVersionTime: json['updateVersionTime'] as String,
      picCover: json['picCover'] as String,
      picCollect: (json['picCollect'] as List<dynamic>)
          .map((e) => PicCollect.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$StyleModelToJson(StyleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'agentId': instance.agentId,
      'merchantId': instance.merchantId,
      'storeId': instance.storeId,
      'styleNum': instance.styleNum,
      'styleName': instance.styleName,
      'sex': instance.sex,
      'version': instance.version,
      'updateVersionTime': instance.updateVersionTime,
      'picCover': instance.picCover,
      'picCollect': instance.picCollect,
    };

PicCollect _$PicCollectFromJson(Map<String, dynamic> json) => PicCollect(
      id: (json['id'] as num).toInt(),
      uuid: json['uuid'] as String,
      url: json['url'] as String,
    );

Map<String, dynamic> _$PicCollectToJson(PicCollect instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uuid': instance.uuid,
      'url': instance.url,
    };
