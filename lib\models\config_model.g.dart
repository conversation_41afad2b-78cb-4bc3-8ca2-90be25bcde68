// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppConfig _$AppConfigFromJson(Map<String, dynamic> json) => AppConfig(
      able: (json['able'] as num).toInt(),
      headImage: json['headImage'] as String,
      carouselImages: (json['carouselImages'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      sounds: json['sounds'] == null
          ? null
          : (json['sounds'] as Map<dynamic, dynamic>).map((key, value) => MapEntry(key, value as String)),
      cdnDomain: json['cdnDomain'] as String,
      version: json['version'] == null ? "" : json['version'] as String,
      algorithmApiUrl: json['algorithmApiUrl'] as String?,
      screenIp: json['screenIp'] as String?,
      currencyType: json['currencyType'] as String?,
    );

Map<String, dynamic> _$AppConfigToJson(AppConfig instance) => <String, dynamic>{
      'able': instance.able,
      'headImage': instance.headImage,
      'carouselImages': instance.carouselImages,
      'cdnDomain': instance.cdnDomain,
      'version': instance.version,
      if (instance.algorithmApiUrl case final value?) 'algorithmApiUrl': value,
      if (instance.screenIp case final value?) 'screenIp': value,
      if (instance.currencyType case final value?) 'currencyType': value,
    };
