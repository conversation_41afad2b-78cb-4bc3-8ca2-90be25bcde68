# Generated code do not commit.
file(TO_CMAKE_PATH "/data/sdk/flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "/data/code/kadaya/flutter" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=/data/sdk/flutter"
  "PROJECT_DIR=/data/code/kadaya/flutter"
  "DART_DEFINES=QVBQX1ZFUlNJT049YXV0by16ZW5nLTEuMS4w"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=true"
  "PACKAGE_CONFIG=/data/code/kadaya/flutter/.dart_tool/package_config.json"
  "FLUTTER_TARGET=lib/main.dart"
)
