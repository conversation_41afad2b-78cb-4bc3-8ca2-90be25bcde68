import 'package:json_annotation/json_annotation.dart';

part 'config_model.g.dart'; // Generated file

@JsonSerializable()
class AppConfig {
  final int able;
  final String headImage;
  final List<String> carouselImages;
  final String cdnDomain;
  final String version;
  final Map<String, String>? sounds;

  @JsonKey(includeIfNull: false)
  final String? algorithmApiUrl;

  @Json<PERSON>ey(includeIfNull: false)
  final String? screenIp;

  @JsonKey(includeIfNull: false)
  final String? currencyType;

  AppConfig({
    required this.able,
    required this.headImage,
    required this.carouselImages,
    required this.cdnDomain,
    required this.version,
    this.sounds,
    this.algorithmApiUrl,
    this.screenIp,
    this.currencyType,
  });

  factory AppConfig.fromJson(Map<String, dynamic> json) =>
      _$AppConfigFromJson(json);

  Map<String, dynamic> toJson() => _$AppConfigToJson(this);
}