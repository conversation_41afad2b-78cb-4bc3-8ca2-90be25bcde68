import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:just_audio/just_audio.dart';
import 'package:kadaya/utils/global.dart';
import 'package:path_provider/path_provider.dart';
import 'package:process_run/process_run.dart';
import 'package:wav/wav_file.dart';

// 自定义播放状态枚举，保持与原代码一致的接口
enum PlayerState {
  playing,
  paused,
  stopped,
}

class SoundPlayer {
  // 单例模式
  static final SoundPlayer _instance = SoundPlayer._internal();

  factory SoundPlayer() => _instance;

  SoundPlayer._internal() {
    // 确保 media_kit 已初始化
    if (Platform.isAndroid) {
      _player = AudioPlayer();

      // 监听播放状态变化
      _player.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          _playerState = PlayerState.stopped;
          _currentAssetPath = null;
        }
      });
    }
  }

  late final AudioPlayer _player;
  PlayerState _playerState = PlayerState.stopped;
  String? _currentAssetPath;
  final Map<String, String> _assetPathCache = {}; // 缓存已提取的资源文件路径

  Future<double?> getWavSeconds(String key) async {
    String? soundPath = await _filePath(key);
    if (soundPath == null) {
      return null;
    }
    try {
      final wav = await Wav.readFile(soundPath);
      return wav.duration;
    } catch (e) {
      return null;
    }
  }

  Future<String?> _filePath(String key) async {
    if (Global.instance.nowSounds.containsKey(key)) {
      return "/data/files/${Global.instance.nowSounds[key]!}";
    } else if (Global.instance.defaultSounds.containsKey(key)) {
      final executableDir = await getApplicationExecutableDirectory();
      return "$executableDir/${Global.instance.defaultSounds[key]!}";
    }
    return null;
  }

  // 播放本地音频文件
  Future<void> play(String key, {double volume = 1.0}) async {
    if (Platform.isLinux) {
      String? soundPath = await _filePath(key);
      if (soundPath == null) {
        print("play sound $key not exits");
        return;
      }
      print("play sound $soundPath");
      try {
        Shell shell =
            Shell(throwOnError: false, runInShell: true, verbose: false);
        shell.run('bash -c "pkill aplay || true; aplay $soundPath"');
      } on ShellException catch (e) {
        print("aplay err: $e");
      }
      return;
    }
    try {
      // 如果正在播放相同文件，则不做任何操作
      // if (_playerState == PlayerState.playing &&
      //     _currentAssetPath == assetPath) {
      //   return;
      // }
      String assetPath = '';
      if (Global.instance.defaultSounds.containsKey(key)) {
        assetPath = Global.instance.defaultSounds[key]!;
      } else {
        return;
      }

      // 停止当前播放
      await _player.stop();

      // 设置音量 (just_audio 使用 0-1 的范围)
      await _player.setVolume(volume);

      // 获取可播放的文件路径（从 assets 提取到临时文件）
      final filePath = await _getPlayableFilePath(assetPath);

      // 播放新文件
      await _player.setFilePath(filePath);
      await _player.play();

      _currentAssetPath = assetPath;
      _playerState = PlayerState.playing;
    } catch (e) {
      if (kDebugMode) {
        print('播放音频失败: $e');
      }
    }
  }

  Future<String> getApplicationExecutableDirectory() async {
    if (kReleaseMode) {
      //final appDocDir = await getApplicationExecutableDirectory();
      final exePath = Platform.resolvedExecutable; // 完整可执行文件路径
      final exeDir = File(exePath).parent.path; // 所在目录
      return '${exeDir}/data/flutter_assets';
    }
    // 获取应用可执行文件所在目录
    final script = Platform.script.toFilePath();
    return Directory(script).parent.path;
  }

  // 从 assets 提取文件到临时目录以便播放
  Future<String> _getPlayableFilePath(String assetPath) async {
    // 检查缓存中是否已有此文件
    if (_assetPathCache.containsKey(assetPath)) {
      final cachedPath = _assetPathCache[assetPath]!;
      final cachedFile = File(cachedPath);
      if (await cachedFile.exists()) {
        return cachedPath;
      }
    }

    // 从 assets 加载文件
    final byteData = await rootBundle.load(assetPath);

    // 创建临时文件
    final tempDir = await getTemporaryDirectory();
    final fileName = assetPath.split('/').last;
    final tempFile = File('${tempDir.path}/$fileName');

    // 写入数据
    await tempFile.writeAsBytes(byteData.buffer.asUint8List());

    // 缓存文件路径
    _assetPathCache[assetPath] = tempFile.path;

    return tempFile.path;
  }

  // 暂停播放
  Future<void> pause() async {
    if (_playerState == PlayerState.playing) {
      await _player.pause();
      _playerState = PlayerState.paused;
    }
  }

  // 恢复播放
  Future<void> resume() async {
    if (_playerState == PlayerState.paused && _currentAssetPath != null) {
      await _player.play();
      _playerState = PlayerState.playing;
    }
  }

  // 停止播放
  Future<void> stop() async {
    await _player.stop();
    _playerState = PlayerState.stopped;
    _currentAssetPath = null;
  }

  // 调整音量 (0.0 到 1.0)
  Future<void> setVolume(double volume) async {
    // media_kit 的音量范围是 0-100
    await _player.setVolume((volume.clamp(0.0, 1.0) * 100).toDouble());
  }

  // 释放资源
  Future<void> dispose() async {
    await _player.stop();
    await _player.dispose();

    // 清理临时文件
    _clearTempFiles();
  }

  // 清理临时文件
  Future<void> _clearTempFiles() async {
    try {
      for (final path in _assetPathCache.values) {
        final file = File(path);
        if (await file.exists()) {
          await file.delete();
        }
      }
      _assetPathCache.clear();
    } catch (e) {
      if (kDebugMode) {
        print('清理临时文件失败: $e');
      }
    }
  }

  // 获取当前播放状态
  PlayerState get playerState => _playerState;

  // 获取当前播放的资源路径
  String? get currentAssetPath => _currentAssetPath;
}
