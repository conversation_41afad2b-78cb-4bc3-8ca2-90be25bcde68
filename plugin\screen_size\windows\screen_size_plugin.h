#ifndef FLUTTER_PLUGIN_SCREEN_SIZE_PLUGIN_H_
#define FLUTTER_PLUGIN_SCREEN_SIZE_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace screen_size {

class ScreenSizePlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  ScreenSizePlugin();

  virtual ~ScreenSizePlugin();

  // Disallow copy and assign.
  ScreenSizePlugin(const ScreenSizePlugin&) = delete;
  ScreenSizePlugin& operator=(const ScreenSizePlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace screen_size

#endif  // FLUTTER_PLUGIN_SCREEN_SIZE_PLUGIN_H_
