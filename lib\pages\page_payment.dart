// payment_page.dart
import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:kadaya/api/algorithm_service.dart';
import 'package:kadaya/api/api_service.dart';
import 'package:kadaya/utils/constants.dart';
import 'package:kadaya/utils/global.dart';
import 'package:kadaya/utils/sound_player.dart';
import 'package:kadaya/utils/toast.dart';
import 'package:kadaya/widgets/button_back.dart';
import 'package:kadaya/widgets/floating_countdown.dart';
import 'package:kadaya/widgets/network_state_wrapper.dart';
import 'package:lottie/lottie.dart';
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../widgets/widget_wave.dart';

enum PaymentStatus {
  qrcode,
  error,
  success,
  printing,
  completed,
}

class PagePayment extends StatefulWidget {
  const PagePayment({super.key});

  @override
  State<PagePayment> createState() => _PagePaymentState();
}

class _PagePaymentState extends State<PagePayment> with RouteAware {
  PaymentStatus _status = PaymentStatus.qrcode;
  bool _isGenerating = false;

  Timer? _timer; // 定时器
  Timer? _timerCompleted;
  int completedSeconds = 35;

  bool _isLoading = false; // 是否正在请求
  bool _isQrcodeLoading = false;

  bool needPrint = true;

  String strQRCode = '';
  Widget? _cacheQrWidget;

  // 倒计时浮层
  late GlobalKey<FloatingCountdownState> floatingCountdownKey;

  // router
  var routeObserver = Global.instance.routeObserver;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void didPopNext() {
    // became visible again
    _didIn();
  }

  @override
  void didPushNext() {
    // new route over me
    _didOut();
  }

  void _didOut() {
    _timer?.cancel();
    _timerCompleted?.cancel();
    floatingCountdownKey.currentState?.stop();
  }

  void _didIn() {
    SoundPlayer().play('paymentQrcode');
    checkOrderDetail();
    floatingCountdownKey = GlobalKey<FloatingCountdownState>();
  }

  @override
  void initState() {
    super.initState();
    _didIn();
  }

  @override
  void dispose() {
    super.dispose();
    _didOut();
  }

  checkOrderDetail() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) async {
      if (!_isLoading && mounted) {
        setState(() {
          _isLoading = true; // 标记为正在请求
        });

        try {
          var response = await ApiService()
              .checkOrder(Global.instance.orderInfo['businessOrderId']);

          if (response['data']['payStatus'] == 1) {
            SoundPlayer().play('paymentPayed');

            floatingCountdownKey.currentState?.stop();

            _timer?.cancel();
            uploadOriginalAndGenerate();
            getQRCode();
            setState(() {
              _status = PaymentStatus.success;
            });
            if (Global.instance.orderInfo['businessData'][0]['printTaskUUID'] !=
                null) {
              printData();
            } else {
              setState(() {
                needPrint = false;
              });
              _stepCompleted();
            }
          }
        } catch (e) {}

        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _stepCompleted() {
    setState(() {
      _status = PaymentStatus.completed;
    });
    if (needPrint) {
      SoundPlayer().play('paymentPrinted');
    }
    _timerCompleted = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (completedSeconds > 0) {
          completedSeconds--;
        } else {
          timer.cancel();
          backToHome(context);
        }
      });
    });
  }

  getQRCode() async {
    if (_isQrcodeLoading) return;
    try {
      setState(() {
        _isQrcodeLoading = true;
      });
      var response = await ApiService()
          .miniCode(Global.instance.orderInfo['businessOrderId']);
      setState(() {
        _isQrcodeLoading = false;
        strQRCode = response['data'];
      });
    } catch (e) {
      setState(() {
        _isQrcodeLoading = false;
      });
    }
  }

  uploadOriginalAndGenerate({int retryCount = 0}) async {
    // ignore: prefer_interpolation_to_compose_strings
    writeLog('上传原图 开始，订单id=' + Global.instance.orderInfo['businessOrderId']);
    String path = '/data/files/${Global.instance.orignalPath}';
    if (Platform.isAndroid) {
      final directory = await getApplicationDocumentsDirectory();
      path = '${directory.path}/${Global.instance.orignalPath}';
    }
    try {
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          path,
          filename: 'original.jpg',
        ),
        'orderNo': Global.instance.orderInfo['businessOrderId'],
      });
      await ApiService()
          .postFormData("/api/order/uploadOriginalAndGenerate", formData);
    } catch (e) {
      // ignore: prefer_interpolation_to_compose_strings
      if (retryCount < 100) {
        writeLog(
            '${'上传原图 结束，订单id=' + Global.instance.orderInfo['businessOrderId']} 上传结果为:失败,进行重试$e');
        await Future.delayed(Duration(seconds: 1)); // 等待1秒后重试
        await uploadOriginalAndGenerate(
            retryCount: retryCount + 1); // 递归调用，增加重试次数
      } else {
        // ignore: prefer_interpolation_to_compose_strings
        writeLog(
            '${'上传原图 结束，订单id=' + Global.instance.orderInfo['businessOrderId']} 上传结果为:失败,已达到最大重试次数');
      }
    }
  }

  void writeLog(String message) async {
    if (Platform.isAndroid) return;
    // 获取当前日期
    String date = DateFormat('yyyyMMdd').format(DateTime.now());

    // 日志目录
    String logDirectory = '/data/flutter/logs';
    String logFilePath = '$logDirectory/$date.log';

    // 获取当前时间
    String timestamp = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());

    // 检查日志目录是否存在，如果不存在则创建
    Directory(logDirectory).create(recursive: true).then((Directory directory) {
      // 写入日志，附加时间戳
      File(logFilePath)
          .writeAsStringSync('$timestamp: $message\n', mode: FileMode.append);
    }).catchError((e) {
      print('Error creating directory: $e');
    });
  }

  String removePrefix(String path, String prefix) {
    if (path.startsWith(prefix)) {
      return path.replaceFirst(prefix, '');
    }
    return path; // 如果路径不以 prefix 开头，则返回原路径
  }

  printData() {
    // SoundPlayer().playLocalAsset('assets/audios/printingWidget.wav');
    // List carts = Global.instance.carts;
    List array = [];
    // for (var cart in carts) {
    //   ProductModel product = cart['products'][0];
    //   if (product.quantity > 0) {
    //     array.add({
    //       'num': product.quantity,
    //       'addr': removePrefix(cart['path'], '/data/files'),
    //       'uuid': cart['uuid']
    //     });
    //   }
    // }

    List list = Global.instance.orderInfo['businessData'];
    for (var business in list) {
      array.add({
        'num': business['goodsNum'],
        'addr': Platform.isAndroid
            ? Uri.parse(business['picUrl']).path
            : business['picUrl'],
        'uuid': business['printTaskUUID'],
        "printerNo": business['printerNo'] ?? ''
      });
    }

    Map<String, dynamic> params = {
      "orderid": Global.instance.orderInfo['businessOrderId'],
      "info": array,
    };
    String url = 'http://127.0.0.1:8081/printcreate';
    if (Platform.isAndroid && Global.instance.appConfig.screenIp != null) {
      url = 'http://' + Global.instance.appConfig.screenIp! + '/printcreate';
    }
    NetworkUtils.postData(url, params).then((result) {
      print('申请打印结果');
      print(result.toString());
      setState(() {
        _status = PaymentStatus.printing;
      });
      checkPrintStatus();
    });
  }

  checkPrintStatus() {
    var lastResponse = "";
    var diffTime = DateTime.now();
    var beginTime = DateTime.now();
    //"businessOrderId" -> "4c89d04fd23749ee"
    _timer = Timer.periodic(const Duration(seconds: 2), (timer) async {
      if (!_isLoading) {
        setState(() {
          _isLoading = true; // 标记为正在请求
        });

        String url = 'http://127.0.0.1:8081/printstatus';
        if (Platform.isAndroid && Global.instance.appConfig.screenIp != null) {
          url = 'http://' +
              Global.instance.appConfig.screenIp! +
              ':8081/printstatus';
        }

        var response;
        try {
          response = await NetworkUtils.getData(url, {
            "orderid": Global.instance.orderInfo['businessOrderId'],
          });
        } catch (e) {
          // 最大超时时间
          if (DateTime.now().difference(diffTime).inSeconds > 60) {
            _timer?.cancel();
            _stepCompleted();
          }
          return;
        } finally {
          setState(() {
            _isLoading = false;
          });
        }

        var isCompleted = true;
        for (var obj in response['status']) {
          if (obj['status'] < 2) {
            isCompleted = false;
          }
        }

        // 30s内容没有变动 打印异常直接结束？
        var nowResponse = response.toString();
        var nowTime = DateTime.now();
        if (lastResponse == "") {
          lastResponse = nowResponse;
        }
        if (lastResponse != nowResponse) {
          diffTime = nowTime;
          lastResponse = nowResponse;
        }
        // 每张大概13s,等两个周期?
        var isLongNoChange = nowTime.difference(diffTime).inSeconds > 30;
        if (isLongNoChange) {
          print("打印结果长时间未更新，直接结束");
          Toast.info(null, '打印正在进行中，请稍事等待~~');
        }

        if (isCompleted || isLongNoChange) {
          _timer?.cancel();
          _stepCompleted();
        }
      }
    });
  }

  Future<void> _generateQRCode() async {
    if (_isGenerating) return;

    setState(() {
      _status = PaymentStatus.qrcode;
      _isGenerating = true;
    });

    // try {
    //   // 模拟网络请求
    //   await Future.delayed(const Duration(seconds: 2));

    //   // 模拟随机失败
    //   if (Random().nextBool()) {
    //     throw Exception('QR Code generation failed');
    //   }

    //   if (!mounted) return;
    //   setState(() {
    //     _status = PaymentStatus.qrcode;
    //     _isGenerating = false;
    //   });

    //   // 开始支付流程
    //   // _startPaymentSequence();
    //   _startStatusSequence();
    // } catch (e) {
    //   if (!mounted) return;
    //   setState(() {
    //     _status = PaymentStatus.error;
    //     _isGenerating = false;
    //   });
    // }
  }

  void _startStatusSequence() async {
    // 5秒后显示支付成功
    await Future.delayed(const Duration(seconds: 5));
    if (!mounted) return;

    setState(() {
      _status = PaymentStatus.success;
    });

    // 再过5秒显示打印中
    await Future.delayed(const Duration(seconds: 5));
    if (!mounted) return;

    setState(() {
      _status = PaymentStatus.printing;
    });

    // 再过5秒显示打印完成
    await Future.delayed(const Duration(seconds: 5));
    if (!mounted) return;

    setState(() {
      _status = PaymentStatus.completed;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kBgColor,
      body: NetworkStateWrapper(
        child: SafeArea(
          child: Stack(
            children: [
              Image.asset(
                "assets/images/bg-black.png",
                fit: BoxFit.cover,
                width: 1.sw,
                height: double.infinity,
              ),

              FloatingCountdown(
                key: floatingCountdownKey,
                seconds: 120,
                showRemainSeconds: 30,
                warningThreshold: 20,
                onCountdownEnd: () => backToHome(context),
                dialogConfirm: true,
              ),

              Column(
                children: [
                  _buildLeaderTip(),
                  Expanded(
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 500),
                      child: _buildContent(),
                    ),
                  ),
                  if (_status == PaymentStatus.qrcode)
                    _buildBackButton(context),
                  SizedBox(height: 125.w),
                ],
              ),

              // if (_status == PaymentStatus.loading) _buildLoadingOverlay(),
              if (_status == PaymentStatus.error) _buildErrorOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    switch (_status) {
      case PaymentStatus.qrcode:
        return _buildQRCodeContent();
      case PaymentStatus.success:
        return _buildSuccessContent();
      case PaymentStatus.printing:
        return _buildPrintingContent();
      case PaymentStatus.completed:
        return _buildCompletedContent();
      default:
        return Container();
    }
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: Container(
          width: 300,
          height: 300,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 100,
                height: 100,
                child: CircularProgressIndicator(
                  strokeWidth: 8,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.blue[400]!,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                "二维码生成中",
                style: TextStyle(
                  fontSize: 24,
                  color: kBgColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: Container(
          width: 300,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.grey[800],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.error_outline,
                size: 90.w,
                color: Colors.white,
              ),
              SizedBox(height: 30.w),
              Column(
                children: [
                  Text(
                    "支付二维码生成失败",
                    style: TextStyle(
                      fontSize: 30.w,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    "Failed to generate payment QR code",
                    style: TextStyle(
                      fontSize: 20.w,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 40.w),
              SizedBox(
                height: 100.w,
                child: ElevatedButton(
                  onPressed: _generateQRCode,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "点击刷新",
                          style: TextStyle(
                            fontSize: 25.w,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          "Click refresh",
                          style: TextStyle(
                            fontSize: 16.w,
                            color: Colors.white,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQRCodeContent() {
    TextStyle colorGreen = TextStyle(
      color: const Color.fromARGB(255, 10, 255, 108),
    );
    TextStyle colorBlue = TextStyle(
      color: const Color.fromARGB(255, 13, 126, 255),
    );

    return Column(
      key: const ValueKey('qrcode'),
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
                width: 400.w,
                height: 400.w,
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: QrImageView(
                  data: Global.instance.orderInfo['payUrl'],
                  size: 400.w,
                )).animate().fadeIn(
              duration: const Duration(milliseconds: 500),
            ),
        SizedBox(height: 40.w),
        Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 32.w,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              children: <TextSpan>[
                TextSpan(text: '请使用'),
                TextSpan(text: '微信', style: colorGreen),
                TextSpan(text: '或'),
                TextSpan(text: '支付宝', style: colorBlue),
                TextSpan(text: '扫码支付'),
              ],
            ),
          ),
          RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 21.w,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              children: <TextSpan>[
                TextSpan(text: 'Use '),
                TextSpan(text: 'WeChat Pay ', style: colorGreen),
                TextSpan(text: 'or '),
                TextSpan(text: 'Alipay ', style: colorBlue),
                TextSpan(text: 'to scan and complete the payment'),
              ],
            ),
          ),
          SizedBox(height: 20.w),
          Text(
            "价格合计：${Global.instance.orderInfo['originalAmount']}元",
            style: TextStyle(
              fontSize: 48.w,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 10.w),
          Text(
            "Total : ￥${Global.instance.orderInfo['originalAmount']}",
            style: TextStyle(
              fontSize: 28.w,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ]).animate().fadeIn(
              duration: const Duration(milliseconds: 500),
              delay: const Duration(milliseconds: 100),
            ),
      ],
    );
  }

  Widget _buildSuccessContent() {
    return Column(
      key: const ValueKey('success'),
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 300.w,
          height: 300.w,
          decoration: const BoxDecoration(
            color: Color(0xFF92E61E),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check,
            size: 200.w,
            color: Colors.white,
          ),
        ).animate().scale(
              duration: const Duration(milliseconds: 500),
              curve: Curves.elasticOut,
            ),
        SizedBox(height: 128.w),
        Column(
          children: [
            Text(
              "支付成功，请稍后",
              style: TextStyle(
                fontSize: 48.w,
                color: Colors.white,
              ),
            ),
            Text(
              "Payment successful, please wait",
              style: TextStyle(
                fontSize: 21.w,
                color: Colors.white,
              ),
            )
          ],
        )
            .animate()
            .fadeIn(
              duration: const Duration(milliseconds: 500),
              delay: const Duration(milliseconds: 200),
            )
            .slideY(
              begin: 0.3,
              curve: Curves.easeOutCubic,
            ),
      ],
    );
  }

  Widget _buildPrintingContent() {
    return Column(
      key: const ValueKey('printing'),
      // mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 200.w,
        ),
        // Text(
        //   "打印中请稍后",
        //   style: TextStyle(
        //     fontSize: 60.w,
        //     color: Colors.white,
        //   ),
        // )
        //     .animate()
        //     .fadeIn(duration: const Duration(milliseconds: 500))
        //     .slideY(begin: 0.3),

        // 打印机图标
        Image.asset(
          'assets/images/print.png',
          width: 604.w,
          height: 453.w,
        ).animate().scale(
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeOutBack,
            ),

        SizedBox(height: 88.w),

        Column(
          children: [
            Text(
              "正在打印请您稍后",
              style: TextStyle(
                fontSize: 60.w,
                color: Colors.white,
              ),
            ),
            Text(
              "Printing in progress, please wait",
              style: TextStyle(
                fontSize: 36.w,
                color: Colors.white,
              ),
            ),
          ],
        )
            .animate()
            .fadeIn(
              duration: const Duration(milliseconds: 500),
              delay: const Duration(milliseconds: 200),
            )
            .slideY(begin: 0.3),

        SizedBox(height: 268.w),

        SizedBox(
          height: 300.w,
          child: Lottie.asset('assets/images/arrow-down.json',
              fit: BoxFit.contain),
        ),
      ],
    );
  }

  Widget _buildBackButton(BuildContext context) {
    return const ButtonBack();
  }

  // 新增打印完成界面
  Widget _buildCompletedContent() {
    return Column(
      key: const ValueKey('completed'),
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(height: 100.w),

        needPrint
            ? Container(
                margin: EdgeInsets.only(left: 40.w, right: 40.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "请取走照片，感谢惠顾",
                      style: TextStyle(
                        fontSize: 60.w,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      "Please take your photo. Thank you for your patronage.",
                      style: TextStyle(
                        fontSize: 23.w,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ))
            : Column(
                children: [
                  Text(
                    "支付完成",
                    style: TextStyle(
                      fontSize: 60.w,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    "Payment Completed",
                    style: TextStyle(
                      fontSize: 30.w,
                      color: Colors.white,
                    ),
                  )
                ],
              )
                .animate()
                .fadeIn(duration: const Duration(milliseconds: 500))
                .slideY(begin: 0.3),

        SizedBox(height: 138.w),

        // 二维码
        GestureDetector(
          onTap: strQRCode != ''
              ? null
              : () {
                  getQRCode();
                },
          child: Container(
            width: 400.w,
            height: 400.w,
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: _buildQrContent(),
          ).animate().scale(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeOutBack,
              ),
        ),

        SizedBox(height: 20.w),

        Column(children: [
          Text(
            "请扫描二维码，领取电子相片",
            style: TextStyle(
              fontSize: 36.w,
              color: Colors.white,
            ),
          ),
          Text(
            "Please scan the QR code to receive the digital photo.",
            style: TextStyle(
              fontSize: 25.w,
              color: Colors.white,
            ),
          )
        ])
            .animate()
            .fadeIn(
              duration: const Duration(milliseconds: 500),
              delay: const Duration(milliseconds: 200),
            )
            .slideY(begin: 0.3),

        SizedBox(height: 210.w),

        // 关闭按钮
        Container(
          width: 460.w,
          height: 120.w,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                BTN_PRIMARY_COLOR,
                BTN_PRIMARY_COLOR,
              ],
            ),
            borderRadius: BorderRadius.circular(75.w),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(75.w),
              onTap: () {
                backToHome(context);
              },
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "关闭",
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 40.w,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          "Close",
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 24.w,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      ],
                    ),
                    SizedBox(
                      width: 30.w,
                    ),
                    Text("${completedSeconds}s",
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 40.w,
                          fontWeight: FontWeight.bold,
                        ))
                  ],
                ),
              ),
            ),
          ),
        )
            .animate()
            .fadeIn(
              duration: const Duration(milliseconds: 500),
              delay: const Duration(milliseconds: 400),
            )
            .slideY(begin: 0.3),
      ],
    );
  }

  Widget _buildLeaderTip() {
    TextStyle baseStyle = TextStyle(
        color: Colors.white,
        fontSize: 50.w,
        fontWeight: FontWeight.bold,
        height: 1);

    TextStyle smallStyle = TextStyle(
      fontSize: 24.w,
    );
    TextStyle colorHighlight = const TextStyle(
      color: Color(0xFF92E61E),
    );

    RichText richText =
        RichText(textAlign: TextAlign.center, text: TextSpan(text: ''));
    switch (_status) {
      case PaymentStatus.qrcode:
        richText = RichText(
          key: const ValueKey('qrcode'),
          textAlign: TextAlign.center,
          text: TextSpan(
            style: baseStyle,
            children: <TextSpan>[
              TextSpan(text: '请'),
              TextSpan(text: '扫码支付\n', style: colorHighlight),
              TextSpan(text: 'Please', style: smallStyle),
              TextSpan(
                  text: 'Please scan to pay\n\n',
                  style: smallStyle.merge(colorHighlight)),
              TextSpan(text: '留下您的打卡印迹\n'),
              TextSpan(text: 'Save your check-in mark', style: smallStyle),
            ],
          ),
        );
        break;
      case PaymentStatus.success:
        richText = RichText(
          key: const ValueKey('success'),
          textAlign: TextAlign.center,
          text: TextSpan(
            style: baseStyle,
            children: <TextSpan>[
              TextSpan(text: '支付成功！\n'),
              TextSpan(text: 'Payment successful!\n\n', style: smallStyle),
              TextSpan(text: '打卡照马上奉上～\n'),
              TextSpan(
                  text: 'Your check -in photo is on its way~',
                  style: smallStyle),
            ],
          ),
        );
        break;
      case PaymentStatus.printing:
      case PaymentStatus.completed:
        richText = RichText(
          key: const ValueKey('printing'),
          textAlign: TextAlign.center,
          text: TextSpan(
            style: baseStyle,
            children: <TextSpan>[
              TextSpan(text: '下方领取照片\n'),
              TextSpan(text: 'Pick up your photo below\n\n', style: smallStyle),
              TextSpan(text: '和朋友'),
              TextSpan(text: '合出套图', style: colorHighlight),
              TextSpan(text: '吧！\n'),
              TextSpan(
                  text: 'Take a fun combo shot ',
                  style: smallStyle.merge(colorHighlight)),
              TextSpan(text: 'with your friends!', style: smallStyle),
            ],
          ),
        );
        break;
      default:
        return Container();
    }

    return Container(
      height: 210.w,
      padding: EdgeInsets.fromLTRB(16.w, 16.w, 165.w, 16.w),
      margin: EdgeInsets.all(30.w),
      child: Row(
        children: [
          const WidgetWave(),
          Expanded(
              child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: richText,
          )),
        ],
      ),
    );
  }

  Widget _buildQrContent() {
    if (_isQrcodeLoading) {
      return Center(
        child: SizedBox(
          width: 160.w,
          height: 160.w,
          child: CircularProgressIndicator(
            strokeWidth: 10.w,
            color: Colors.orange,
          ),
        ),
      );
    }

    if (strQRCode == '') {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, color: Colors.red, size: 180.w),
          const SizedBox(height: 16),
          Text(
            '获取二维码失败',
            style: TextStyle(
              color: Colors.red,
              fontSize: 32.w,
            ),
          ),
          Text(
            '点击重试',
            style: TextStyle(
              color: Colors.red,
              fontSize: 32.w,
            ),
          )
        ],
      );
    } else {
      _cacheQrWidget ??= Image.memory(
        base64Decode(strQRCode.split(',').last),
        fit: BoxFit.contain,
      );
      return _cacheQrWidget!;
    }
  }
}
