// network_utils.dart
import 'dart:convert';

import 'package:http/http.dart' as http;

class NetworkUtils {
  // 发送 POST 请求的静态方法
  static Future<Map<String, dynamic>> postData(
      String url, Map<String, dynamic> body) async {
    try {
      print('开始请求:' + url + '参数' + body.toString());
      // 发送 POST 请求
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json; charset=UTF-8', // 设置请求头
        },
        body: jsonEncode(body), // 将请求体编码为 JSON
      );

      // 检查响应状态码
      if (response.statusCode == 200 || response.statusCode == 201) {
        print('请求结果:' + response.body);
        // 请求成功，解析响应数据
        return jsonDecode(response.body);
      } else {
        // 请求失败，抛出异常
        // throw Exception('Request failed with status: ${response.statusCode}');
        return {"error": '网络错误：${response.body}'};
      }
    } catch (e) {
      return {"error": "网络错误：${e.toString()}"};
    }
  }

  static Future<Map<String, dynamic>> getData(
      String baseUrl, Map<String, dynamic> queryParameters) async {
    try {
      print('开始请求数据$baseUrl');

      // 将参数拼接到 URL 中
      String url = '$baseUrl?';
      queryParameters.forEach((key, value) {
        url += '$key=$value&';
      });
      url = url.substring(0, url.length - 1); // 去掉最后一个多余的 '&'

      // 发送 GET 请求
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json; charset=UTF-8', // 设置请求头
        },
      );

      // 检查响应状态码
      if (response.statusCode == 200) {
        print('返回结果${response.body}');
        // 请求成功，解析响应数据
        return jsonDecode(response.body);
      } else {
        // 请求失败，抛出异常
        throw Exception('Request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      // 捕获异常并抛出
      throw Exception('Error: $e');
    }
  }
}
