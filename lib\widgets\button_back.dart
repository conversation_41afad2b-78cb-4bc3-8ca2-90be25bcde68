import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ButtonBack extends StatelessWidget {
  const ButtonBack({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 0.4.sw,
      child: TextButton(
        onPressed: () {
          Navigator.pop(context);
        },
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.arrow_back,
                  size: 40.w,
                  color: Colors.white.withAlpha((255.0 * 0.6).round())),
              SizedBox(width: 30.w),
              Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "返回上一步",
                    style: TextStyle(
                      color: Colors.white.withAlpha((255.0 * 0.6).round()),
                      fontSize: 20.w,
                    ),
                  ),
                  Text(
                    "Go back",
                    style: TextStyle(
                      color: Colors.white.withAlpha((255.0 * 0.6).round()),
                      fontSize: 18.w,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
