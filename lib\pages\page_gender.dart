// lib/pages/home_page.dart
import 'dart:async';
import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gif/gif.dart';
import 'package:kadaya/pages/page_capture.dart';
import 'package:kadaya/utils/constants.dart';
import 'package:kadaya/utils/global.dart';
import 'package:kadaya/utils/sound_player.dart';
import 'package:kadaya/widgets/widget_wave.dart';

import '../widgets/floating_countdown.dart';

class PageGender extends StatefulWidget {
  PageGender({super.key});

  @override
  State<PageGender> createState() => _PageGenderState();
}

class _PageGenderState extends State<PageGender> with RouteAware {
  Timer? _timerPlay;

  int _currentIndex = -1;
  late List<String> carouselImages;
  late Map<String, Widget> cacheWidgets;

  var genders = [
    [
      "assets/images/gender/man.png",
      "assets/images/gender/man-1.png",
      "男生",
      "Male"
    ],
    [
      "assets/images/gender/women.png",
      "assets/images/gender/women-1.png",
      "女生",
      "Female"
    ],
    [
      "assets/images/gender/boy.png",
      "assets/images/gender/boy-1.png",
      "男孩",
      "Boy"
    ],
    [
      "assets/images/gender/girl.png",
      "assets/images/gender/girl-1.png",
      "女孩",
      "Girl"
    ],
  ];

  late GlobalKey<FloatingCountdownState> floatingCountdownKey;

  // router
  var routeObserver = Global.instance.routeObserver;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void didPopNext() {
    // became visible again
    _didIn();
  }

  @override
  void didPushNext() {
    // new route over me
    _didOut();
  }

  void _didOut() {
    floatingCountdownKey.currentState?.stop();
  }

  void _didIn() {
    SoundPlayer().play('genderSelect');
    floatingCountdownKey = GlobalKey<FloatingCountdownState>();
  }

  @override
  void initState() {
    super.initState();
    cacheWidgets = HashMap();
    carouselImages = Global.instance.appConfig.carouselImages;
    _didIn();
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    _didOut();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Image.asset(
            "assets/images/bg-black.png",
            fit: BoxFit.cover,
            width: 1.sw,
          ),
          Column(
              mainAxisSize: MainAxisSize.max,
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              // spacing: 50.w,
              children: [
                // header
                _buildLeaderTip(),

                SizedBox(height: 56.w),

                _buildTip(),

                SizedBox(height: 80.w),

                _buildMenus(),
              ]),
          FloatingCountdown(
            key: floatingCountdownKey,
            seconds: 20,
            maxSeconds: 60,
            showRemainSeconds: 10,
            warningThreshold: 5,
            bottom: 0.40.sh,
            onCountdownEnd: () =>
                // Navigator.of(context).popUntil((route) => route.isFirst),
                backToHome(context),
          ),
        ],
      ),
    );
  }

  Widget _buildTip() {
    return Row(children: [
      SizedBox(
        width: 20.w,
      ),
      Column(
        children: [
          Text(
            "错误示范",
            style: TextStyle(
                color: Colors.white,
                fontSize: 48.w,
                fontWeight: FontWeight.bold),
          ),
          Text(
            "Incorrect Examples:",
            style: TextStyle(
                color: Colors.white,
                fontSize: 21.w,
                fontWeight: FontWeight.bold),
          )
        ],
      ),
      SizedBox(
        width: 50.w,
      ),
      Expanded(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Image.asset(
              "assets/images/gender/tip-1.png",
              width: 190.w,
              fit: BoxFit.fitWidth,
            ),
            Image.asset(
              "assets/images/gender/tip-2.png",
              width: 190.w,
              fit: BoxFit.fitWidth,
            ),
            Image.asset(
              "assets/images/gender/tip-3.png",
              width: 190.w,
              fit: BoxFit.fitWidth,
            ),
          ],
        ),
      ),
      SizedBox(
        width: 150.w,
      ),
    ]);
  }

  Widget _buildMenuItem(int index) {
    var item = genders[index];
    return InkWell(
        onTap: () {
          floatingCountdownKey.currentState?.reset();
          setState(() {
            _currentIndex = index;
          });
        },
        child: SizedBox(
          width: 0.45.sw,
          height: 0.3.sw,
          // color: Colors.red,
          child: Row(children: [
            AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: _currentIndex == index
                    ? Transform.scale(
                        scale: 1.03,
                        child: Image.asset(
                          key: ValueKey("${index}_0"),
                          item[1],
                          width: 0.23.sw,
                          fit: BoxFit.fitWidth,
                        ))
                    : Image.asset(
                        key: ValueKey("${index}_1"),
                        item[0],
                        width: 0.23.sw,
                        fit: BoxFit.fitWidth,
                      )),
            SizedBox(
              width: 36.w,
            ),
            Container(
                width: 0.15.sw,
                child: Stack(children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 20.w,
                      ),
                      Text(
                        item[2],
                        style: TextStyle(
                            color: Colors.black,
                            fontSize: 64.w,
                            fontWeight: FontWeight.bold),
                      ),
                      Text(
                        item[3],
                        style: TextStyle(
                            color: Colors.black,
                            fontSize: 21.w,
                            fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  Positioned(
                    left: 10.w,
                    bottom: 10.w,
                    child: _currentIndex == index
                        ? Gif(
                            width: 120.w,
                            autostart: Autostart.once,
                            duration: const Duration(milliseconds: 800),
                            image: const AssetImage(
                                "assets/images/gender/active.gif"),
                          )
                        : Container(),
                  )
                ]))
          ]),
        ));
  }

  Widget _buildMenus() {
    return Expanded(
        child: Container(
      padding: EdgeInsets.only(top: 60.w, left: 80.w),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(120.w),
              topRight: Radius.circular(120.w))),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildMenuItem(0),
              _buildMenuItem(1),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildMenuItem(2),
              _buildMenuItem(3),
            ],
          ),
          _buildButton(),
          SizedBox(
            height: 100.w,
          ),
        ],
      ),
    ));
  }

  Widget _buildButton() {
    return AnimatedContainer(
      width: 1.sw - 100.w,
      height: 146.w,
      decoration: BoxDecoration(
        color: _currentIndex >= 0
            ? BTN_PRIMARY_COLOR
            : const Color.fromARGB(255, 229, 229, 229),
        borderRadius: BorderRadius.circular(75.w),
      ),
      duration: const Duration(microseconds: 300),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(75.w),
          onTap: _currentIndex >= 0
              ? () {
                  //todo 过滤对应的性别
                  Global.instance.currentStyleModel =
                      Global.instance.styleModels[_currentIndex];

                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PageCapture()),
                  );
                }
              : null,
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "开 始",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 64.w,
                          fontWeight: FontWeight.bold),
                    ),
                    Text(
                      "Start",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 24.w,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLeaderTip() {
    TextStyle baseStyle = TextStyle(
        fontFamily: 'DouyinSansBold',
        color: Colors.white,
        fontSize: 50.w,
        fontWeight: FontWeight.bold,
        height: 1);

    TextStyle smallStyle = TextStyle(
      fontSize: 24.w,
    );
    TextStyle colorHighlight = TextStyle(
      color: Color(0xFF92E61E),
    );

    return Container(
      height: 210.w,
      padding: EdgeInsets.fromLTRB(16.w, 16.w, 165.w, 16.w),
      margin: EdgeInsets.all(30.w),
      child: Row(
        children: [
          const WidgetWave(),
          Expanded(
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: baseStyle,
                children: <TextSpan>[
                  TextSpan(text: '别学下面那些错误姿势\n'),
                  TextSpan(
                      text: 'Don\'t imitatetheincorrect poses below!\n\n',
                      style: smallStyle),
                  TextSpan(text: '快'),
                  TextSpan(text: '选一个类型', style: colorHighlight),
                  TextSpan(text: '拍美照吧\n'),
                  TextSpan(
                      text: 'Choose a category ',
                      style: smallStyle.merge(colorHighlight)),
                  TextSpan(
                      text: 'and take your perfect photo!', style: smallStyle),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
