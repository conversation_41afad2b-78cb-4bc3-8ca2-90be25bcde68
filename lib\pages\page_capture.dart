import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:animate_do/animate_do.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as MImage;
import 'package:intl/intl.dart';
import 'package:kadaya/pages/page_cart.dart';
import 'package:kadaya/utils/constants.dart';
import 'package:kadaya/utils/file_saver.dart';
import 'package:kadaya/utils/global.dart';
import 'package:kadaya/utils/sound_player.dart';
import 'package:kadaya/utils/toast.dart';
import 'package:kadaya/widgets/floating_countdown.dart';
import 'package:kadaya/widgets/widget_wave.dart';
import 'package:lottie/lottie.dart';
import 'package:path_provider/path_provider.dart';

enum CaptureState {
  preparing,
  reviewing,
}

class PageCapture extends StatefulWidget {
  const PageCapture({super.key});

  @override
  State<PageCapture> createState() => _PageCaptureState();
}

class _PageCaptureState extends State<PageCapture> with RouteAware {
  CaptureState _state = CaptureState.preparing;
  int _countdown = 3;
  Timer? _timer;

  // WebRTC variables (for Linux)
  RTCVideoRenderer? _localRenderer;

  // Camera variables (for Android)
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;

  Uint8List? avatar;
  int direction = Global.instance.cameraDirection;

  bool _isAgreed = true; // Checkbox state

  bool _showDialogTip = true;

  bool isLoading = false;
  late GlobalKey<FloatingCountdownState> floatingCountdownKey;

  // router
  var routeObserver = Global.instance.routeObserver;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void didPopNext() {
    // became visible again
    _didIn();
  }

  @override
  void didPushNext() {
    // new route over me
    _didOut();
  }

  void _didOut() {
    _timer?.cancel();
    if (Platform.isLinux) {
      Global.webRTCService.setStreamChangeCallback(null);
      _localRenderer?.dispose();
    } else {
      _cameraController?.dispose();
    }
    floatingCountdownKey.currentState?.stop();
  }

  void _didIn() {
    floatingCountdownKey = GlobalKey<FloatingCountdownState>();
    _initializeCamera();
    SoundPlayer().play('captureTip');
  }

  @override
  void initState() {
    super.initState();
    _didIn();
  }

  @override
  void dispose() {
    _didOut();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    if (Platform.isLinux) {
      await _initializeWebRTC();
    } else {
      await _initializeNativeCamera();
    }
  }

  Future<void> _initializeWebRTC() async {
    _localRenderer = RTCVideoRenderer();

    try {
      await _localRenderer!.initialize();

      _localRenderer!.srcObject = Global.webRTCService.getStream();

      if (mounted) {
        setState(() {});
        // _startCountdown();
      }
    } catch (e) {
      debugPrint('Error accessing camera via WebRTC: $e');
    }

    // Stream change callback
    Global.webRTCService.setStreamChangeCallback((MediaStream? stream) {
      _localRenderer!.srcObject = stream;
    });
  }

  Future<void> _initializeNativeCamera() async {
    try {
      _cameras = await availableCameras();
      if (_cameras!.isEmpty) {
        debugPrint('No cameras available');
        return;
      }

      // Select front camera by default
      final frontCameras = _cameras!
          .where((camera) => camera.lensDirection == CameraLensDirection.front)
          .toList();

      final selectedCamera =
          frontCameras.isNotEmpty ? frontCameras.first : _cameras!.first;

      _cameraController = CameraController(
        selectedCamera,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      await _cameraController!.initialize();

      if (mounted) {
        setState(() {});
        _startCountdown();
      }
    } catch (e) {
      debugPrint('Error initializing camera: $e');
    }
  }

  void _startCountdown() {
    SoundPlayer().play('captureBegin');
    setState(() {
      // _countdown = _timer == null ? 5 : 3;
      _countdown = 5;
      _state = CaptureState.preparing;
    });

    floatingCountdownKey.currentState?.reset();

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_countdown > 1) {
            _countdown--;
          } else {
            _timer?.cancel();
            _takePhoto();
          }
        });
      }
    });
  }

  Future<void> _takePhoto() async {
    try {
      MImage.Image? _image;

      if (Platform.isLinux) {
        // WebRTC capture for Linux
        (int, int, ByteBuffer)? res = await Global.webRTCService.captureFrame();
        if (res != null) {
          _image = MImage.Image.fromBytes(
              order: MImage.ChannelOrder.rgba,
              width: res.$1,
              height: res.$2,
              bytes: res.$3);
        }
      } else {
        delCacheImages();
        // Native camera capture for Android
        final XFile file = await _cameraController!.takePicture();
        var buffer = await file.readAsBytes();
        Global.instance.originalBase64 = base64Encode(buffer);
        _image = MImage.decodeImage(buffer)!;
      }

      if (_image != null) {
        Uint8List img = flipHorizontalManual(cropToSquare(_image));
        setState(() {
          avatar = img;
          _state = CaptureState.reviewing;
        });
        saveImage();
        SoundPlayer().play('captureDone');
      } else {
        Toast.error(
            null, "拍照错误，请检查摄像头\nCamera error, please check the camera.");
        await Future.delayed(const Duration(seconds: 1));
        _startCountdown();
      }
    } catch (e) {
      debugPrint('Error taking photo: $e');
    }
  }

  void delCacheImages() async {
    final dir = await getTemporaryDirectory();

    final fullPath = '${dir.path}/temp';
    final directory = Directory(fullPath);
    final exists = await directory.exists();

    if (exists) {
      // 列出目录下的所有内容
      final entities = directory.list(recursive: false);

      // 遍历并删除所有文件
      await for (final entity in entities) {
        if (entity is File) {
          await entity.delete();
          print('已删除文件: ${entity.path}');
        }
      }
    }
  }

  void saveImage() {
    // Get current time
    DateTime now = DateTime.now();

    // Format as YYYYMMDD
    String formattedDate = DateFormat('yyyyMMdd').format(now);
    int timestampMillis = now.millisecondsSinceEpoch;
    String path =
        'screen/$formattedDate/${Global.instance.screenNumber}/$timestampMillis/original.jpg';
    Global.instance.orignalPath = path;
    Global.instance.flowUuid = timestampMillis.toString();

    FileSaver.saveFile(data: compressImageToJpg(avatar!), fileName: path);
  }

  // Manually implement horizontal mirror flip
  Uint8List flipHorizontalManual(Uint8List imageData) {
    final image = MImage.decodeImage(imageData)!;
    final width = image.width;
    final height = image.height;

    for (var y = 0; y < height; y++) {
      for (var x = 0; x < width ~/ 2; x++) {
        final leftColor = image.getPixelInterpolate(x, y);
        final rightColor = image.getPixelInterpolate(width - 1 - x, y);

        image.setPixel(x, y, rightColor);
        image.setPixel(width - 1 - x, y, leftColor);
      }
    }

    final image2 = Platform.isLinux
        ? MImage.copyRotate(image, angle: 90 * direction)
        : image;

    return Uint8List.fromList(MImage.encodePng(image2));
  }

  // Crop image to square
  Uint8List cropToSquare(MImage.Image image) {
    // Decode image

    // Get image width and height
    final width = image.width;
    final height = image.height;

    // Calculate crop area
    final size =
        width < height ? width : height; // Square side is the shorter dimension
    final x = (width - size) ~/ 2; // Center horizontally
    final y = (height - size) ~/ 2; // Center vertically

    // Crop image
    final croppedImage =
        MImage.copyCrop(image, x: x, y: y, width: size, height: size);

    // Encode cropped image as Uint8List
    return Uint8List.fromList(MImage.encodePng(croppedImage));
  }

  /// Compress Uint8List image and save as JPG format
  Uint8List compressImageToJpg(Uint8List imageData, {int quality = 85}) {
    // Decode Uint8List to Image object
    final image = MImage.decodeImage(imageData);

    // Encode Image object as JPG format Uint8List
    Uint8List jpgData =
        Uint8List.fromList(MImage.encodeJpg(image!, quality: quality));

    return jpgData;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            SizedBox(
              width: 1.sw,
            ),
            AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: _state == CaptureState.preparing
                    ? Image.asset(
                        key: const ValueKey("bg_0"),
                        "assets/images/bg-black.png",
                        fit: BoxFit.cover,
                        width: 1.sw,
                      )
                    : Image.asset(
                        key: const ValueKey("bg_1"),
                        "assets/images/bg-green.png",
                        fit: BoxFit.cover,
                        width: 1.sw,
                      )
                        .animate()
                        .scale(
                            duration: const Duration(milliseconds: 300),
                            begin: const Offset(1.5, 1.5),
                            end: const Offset(1, 1))
                        .fadeIn(duration: const Duration(milliseconds: 600))),

            if (_state == CaptureState.reviewing)
              Container(
                width: 1.sw,
                padding: EdgeInsets.only(top: 60.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      "哇！好棒",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 80.w,
                          fontWeight: FontWeight.bold),
                    ),
                    Text(
                      "Wow! Awesome",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 24.w,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ).animate().bounceInUp(
                    from: 30.w,
                    duration: const Duration(milliseconds: 450),
                    delay: const Duration(milliseconds: 100),
                  ),

            FloatingCountdown(
              key: floatingCountdownKey,
              seconds: 30,
              maxSeconds: 60,
              showRemainSeconds: 15,
              warningThreshold: 10,
              bottom: 0.30.sh,
              onCountdownEnd: () => backToHome(context),
            ),

            // Camera rotation button
            if (_state == CaptureState.preparing && Platform.isLinux)
              Positioned(
                right: 0.w,
                top: 0.w,
                child: GestureDetector(
                  child: Icon(
                    Icons.refresh_rounded,
                    color: Colors.transparent,
                    size: 60.w,
                  ),
                  onTap: () {
                    setState(() {
                      direction++;
                      if (direction > 4) {
                        direction = 1;
                      }
                      Global.instance.cameraDirection = direction;
                      FileSaver.writeJsonFile(
                          {'direction': direction}, APP_PATH_FLUTTER);
                    });
                  },
                ),
              ),

            if (_state == CaptureState.preparing)
              LeaderTipOverlay(
                countDown: _countdown,
              ),

            // Camera preview or photo
            Center(
              child: _buildPreviewArea(),
            ),

            // Bottom area
            Positioned(
              left: 0,
              right: 0,
              bottom: 40,
              height: 20,
              child: Container(),
            ),

            if (_showDialogTip)
              _buildDialogTip().animate().fadeIn().zoomIn(
                    duration: const Duration(milliseconds: 450),
                    delay: const Duration(milliseconds: 100),
                  ),
          ],
        ),
      ),
      // floatingActionButton: _buildAgreement()
    );
  }

  Widget _buildDialogTipButton() {
    return Container(
      width: 1.sw - 300.w,
      height: 130.w,
      decoration: BoxDecoration(
        color: BTN_PRIMARY_COLOR,
        borderRadius: BorderRadius.circular(75.w),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(75.w),
          onTap: () async {
            setState(() {
              _showDialogTip = false;
            });
            floatingCountdownKey.currentState?.reset();
            _startCountdown();
          },
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "准备好了",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 64.w,
                          fontWeight: FontWeight.bold),
                    ),
                    Text(
                      "I'm ready!",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 24.w,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDialogTip() {
    return Stack(
      fit: StackFit.expand,
      children: [
        Container(
          width: 1.sw,
          height: 1.sw,
          color: Colors.black26,
        ),
        Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 60.w, right: 30.w),
                    child: Image.asset(
                      "assets/images/dialog-tip-bg.jpg",
                      width: 1.sw,
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                  Positioned(
                      left: 95.w,
                      top: 465.w,
                      child: SizedBox(
                        width: 100.w,
                        height: 100.w,
                        child: Lottie.asset('assets/images/animate-radio.json',
                            repeat: true, fit: BoxFit.contain),
                      )),
                  Positioned(
                      left: 535.w,
                      top: 465.w,
                      child: SizedBox(
                        width: 100.w,
                        height: 100.w,
                        child: Lottie.asset('assets/images/animate-radio.json',
                            repeat: true, fit: BoxFit.contain),
                      ))
                ],
              ),
              SizedBox(
                height: 100.w,
              ),
              SizedBox(
                width: 600.w,
                child: _buildDialogTipButton(),
              ),
              SizedBox(
                height: 140.w,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAgreement() {
    return Padding(
      padding: EdgeInsets.only(right: 66.w, bottom: 106.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // User agreement text
          GestureDetector(
            onTap: _showUserAgreementDialog,
            child: Text(
              '用户服务和隐私授权协议',
              style: TextStyle(
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                  fontSize: 40.w),
            ),
          ),
          SizedBox(width: 16.w), // Add spacing
          // Checkbox
          GestureDetector(
            onTap: () {
              setState(() {
                _isAgreed = !_isAgreed;
              });
            },
            child: Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: _isAgreed ? Colors.blue : Colors.transparent,
                borderRadius: BorderRadius.all(Radius.circular(4.w)),
                border: Border.all(
                  color: _isAgreed ? Colors.blue : Colors.grey,
                  width: 2.w,
                ),
              ),
              child: _isAgreed
                  ? Icon(
                      Icons.check,
                      size: 30.w,
                      color: Colors.white,
                    )
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  // Show user agreement dialog
  void _showUserAgreementDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('用户服务和信息存储告知'),
          content: SingleChildScrollView(
            child: Text(
              '欢迎您使用 咔哒鸭旅拍机 为您提供的"一拍即合、快捷成像"服务！\n在您使用旅拍机服务前，咔哒鸭旅拍机开发者北京天翔睿翼科技有限公司向您郑重做如下告知：\n基础提示\n在使用咔哒鸭旅拍机服务之前，请您务必审慎阅读、充分理解本告知(如果您未满16周岁，或已满16周岁未满18周岁且不能以自己的劳动收入作为主要收入来源的，请在法定监护人的陪同下阅读本告知）各项条款，特别是限制或免除责任条款、隐私保护条款等，以加粗加黑和/或加下划线等显示形式提示您注意的重要条款，请务必重点查阅。\n若您不同意本告知，则您有充分且完全的权利退出咔哒鸭旅拍机服务，您实际使用咔哒鸭旅拍机的行为即视为您已阅读、理解并同意接受本告知。如果您对本告知有任何的疑问、投诉、意见和建议，欢迎您通过本告知所附联系方式与我们沟通反馈。\n\n服务内容\n咔哒鸭旅拍机为您提供丰富的服务，用户只需简单操作，即可快速捕捉面部图像，依托前沿AI智能生成技术，瞬间生成与各大景区及特色主题妆容完美契合的照片并进一步定制成别具一格的文旅纪念品，诸如明信片、冰箱贴等。\n您同意并知悉，咔哒鸭旅拍机的具体服务内容、功能和形式由旅拍机根据实际情况按实际可见的状态提供，我们有权自行确定旅拍机服务的具体内容、功能和形式，有权自行决定增加、变更、中断和停止旅拍机具体的内容、功能和形式。具体以旅拍机实时呈现的服务内容、功能和形式为准。\n信息适用与存储豁免\n当您使用咔哒鸭拍照时，我们会收集您拍摄的照片信息。这些信息会通过AI智能生成技术，用于生成特色主题妆容契合的照片。同时为您提供更完美的服务体验，该信息也将会纳入我们的数据库，我们会进行科学管理。\n对于收集到的照片信息，我们会先进行匿名化处理，将其转化为无法识别您个人身份的形式之后再以数据的形式加以存储，存储过程中我们始终严格遵循隐私保护原则，不遗余力地保障您的信息安全。\n当您使用咔哒鸭进行拍照操作时，即代表您认可并同意我们有权依据此信息保护规则，对与您的照片信息进行使用与存储。考虑到数据库优化的实际需求，就信息数据的存储事项，您同意豁免我们另行单独征得您的授权。\n如何联系我们\n如果您有任何的疑问、投诉、意见和建议，欢迎您与我们沟通反馈。我们的联系方式见下：\n客服热线： 010-88738088 \n客服邮箱： <EMAIL> \n日期： 2024 年 05 月 01 日',
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('关闭'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPreviewArea() {
    return Stack(
      children: [
        Column(
          children: [
            Container(
              width: 800.w,
              height: 800.w,
              margin: EdgeInsets.only(top: Platform.isLinux ? 400.w : 300.w),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 4.w),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha((255.0 * 0.1).round()),
                    blurRadius: 10.w,
                    offset: Offset(0.w, 5.w),
                  ),
                ],
              ),
              child: ClipOval(
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    _state == CaptureState.preparing
                        ? _buildCameraPreview()
                        : _buildPhotoPreview(),
                    Image.asset(
                      "assets/images/camera-shape.png",
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(
              height: 30.w,
            ),

            // tip
            _buildTip(),

            SizedBox(
              height: 120.w,
            ),

            // buttons
            if (_state == CaptureState.reviewing) _buildBottomSection()
          ],
        ),
      ],
    );
  }

  Widget _buildCameraPreview() {
    if (Platform.isLinux && _localRenderer != null) {
      return Stack(
        fit: StackFit.expand,
        children: [
          Transform.rotate(
            angle: 1.5708 * direction,
            child: RTCVideoView(
              _localRenderer!,
              mirror: true,
              filterQuality: FilterQuality.high,
              objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
            ),
          ),
          if (_state == CaptureState.preparing)
            Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Text(
                    '$_countdown',
                    style: TextStyle(
                      fontSize: 200.w,
                      fontWeight: FontWeight.bold,
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = 5.w
                        ..color = const Color.fromARGB(114, 0, 0, 0),
                    ),
                  ),
                  Text(
                    '$_countdown',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 200.w,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
        ],
      );
    } else if (_cameraController != null &&
        _cameraController!.value.isInitialized) {
      // Calculate aspect ratio to fit the camera preview in a circle
      final size = MediaQuery.of(context).size;
      // final scale =
      //     1 / (_cameraController!.value.aspectRatio * size.aspectRatio);

      return Stack(
        fit: StackFit.expand,
        children: [
          // Center(
          //   child: SizedBox(
          //     width: 800.w,
          //     height: 800.w * size.height / size.width,
          //     child: CameraPreview(_cameraController!),
          //   ),
          // ),
          ClipRect(
            child: FittedBox(
              fit: BoxFit.cover,
              child: SizedBox(
                width: 800.w,
                height: 800.w * size.height / size.width,
                child: CameraPreview(_cameraController!),
              ),
            ),
          ),
          if (_state == CaptureState.preparing)
            Center(
              child: Text(
                '$_countdown',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 200.w,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      );
    }

    return Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildPhotoPreview() {
    if (avatar != null) {
      return Image.memory(
        avatar!,
        fit: BoxFit.cover,
      );
    }
    return const SizedBox();
  }

  Widget _buildTip() {
    return GestureDetector(
      onTap: () {
        backToHome(context);
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "请保持头部在拍照框内",
            style: TextStyle(
              color: _state == CaptureState.reviewing
                  ? Colors.black.withAlpha((255.0 * 0.8).round())
                  : Colors.white.withAlpha((255.0 * 0.8).round()),
              fontSize: 36.w,
            ),
          ),
          Text(
            "Please keep your head within the frame",
            style: TextStyle(
              color: _state == CaptureState.reviewing
                  ? Colors.black.withAlpha((255.0 * 0.8).round())
                  : Colors.white.withAlpha((255.0 * 0.8).round()),
              fontSize: 24.w,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          ElevatedButton(
            onPressed: isLoading ? null : _startCountdown,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black54,
              foregroundColor: Colors.black54,
              padding: EdgeInsets.fromLTRB(
                75.w,
                30.w,
                75.w,
                30.w,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(120.w),
              ),
            ),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "重新拍照",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 45.w,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 10.w),
                  ),
                  Text(
                    "Retake Photo",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 21.w,
                    ),
                  )
                ],
              ),
            ),
          ),

          SizedBox(width: 60.w),

          // Confirm button
          ElevatedButton(
              onPressed: isLoading
                  ? null
                  : () async {
                      if (!_isAgreed) {
                        Toast.error(context, '请先同意用户服务和隐私授权协议');
                        return;
                      }

                      var isOK = await faceDetect();
                      if (!isOK) {
                        _startCountdown();
                        return;
                      }

                      Global.instance.styles = [];
                      Global.instance.carts = [];
                      Global.instance.allProducts = [];
                      Global.instance.indexSwiper = 0;
                      Global.instance.isFirst = true;

                      await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const PageCart(),
                          ));
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                padding: EdgeInsets.fromLTRB(
                  75.w,
                  30.w,
                  75.w,
                  30.w,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(120.w),
                ),
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "生成图片",
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 45.w,
                            letterSpacing: 10.w,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          "Generate Image",
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 21.w,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Future<bool> faceDetect() async {
    if (isLoading) return false;
    setState(() {
      isLoading = true;
    });

    dynamic body;
    String url;
    if (Platform.isAndroid) {
      url = Global.instance.appConfig.algorithmApiUrl ??
          'http://192.168.123.140:8920/faceDetect';
      body = {
        'imageBase64': Global.instance.originalBase64,
      };
    } else {
      url = 'http://127.0.0.1:8082/faceDetect';
      body = {
        'imagePath':
            '${Global.instance.filePath}/${Global.instance.orignalPath}'
      };
    }
    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(body),
      );
      if (!(response.statusCode == 200 || response.statusCode == 201)) {
        throw Exception("http status: ${response.statusCode}");
      }
      var data = jsonDecode(response.body);
      if (data['code'] == '200' || data['code'] == '100') {
        return true;
      }
      Toast.warning(null, "未检测到人脸\nNo face detected");
    } catch (e) {
      print("人脸检测错误 ${e}");
      Toast.warning(null, "发生错误\nAn error occurred");
      return false;
    } finally {
      setState(() {
        isLoading = false;
      });
    }
    return false;
  }
}

class LeaderTipOverlay extends StatelessWidget {
  int countDown;

  LeaderTipOverlay({super.key, required this.countDown});

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      /* Positioned(
        bottom: 10.w,
        left: 0.5.sw - 100.w,
        child: Transform.rotate(
            angle: 45 * (pi / 180),
            child: Container(
              width: 200.w,
              height: 200.w,
              decoration: BoxDecoration(
                  color: const Color(0xFFB5E045),
                  borderRadius: BorderRadius.all(Radius.circular(40.w))),
            )),
      ),*/

      Positioned(
        bottom: -30.w,
        left: 0.5.sw - 50.w,
        child: Icon(
          Icons.arrow_drop_down,
          size: 100.w,
          color: BTN_PRIMARY_COLOR,
        ),
      ),
      Container(
        height: 210.w,
        padding: EdgeInsets.fromLTRB(16.w, 16.w, 165.w, 16.w),
        margin: EdgeInsets.all(30.w),
        // decoration: BoxDecoration(
        //   color: const Color(0xFFB5E045),
        //   borderRadius: BorderRadius.circular(105.w),
        // ),
        child: Row(
          children: [
            /*Transform.translate(
              offset: Offset(-10.w, 0),
              child: Container(
                width: 210.w,
                height: 210.w,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    "$countDown",
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 120.w,
                        fontWeight: FontWeight.w500,
                        height: 1),
                  ),
                ),
              ),
            ),*/

            const WidgetWave(),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildTextLine('请看上方摄像头', 'Look at the camera above'),
                  SizedBox(height: 20.w),
                  _buildTextLine('马上为您拍照啦', 'your photo will be taken soon!'),
                ],
              ),
            ),
            SizedBox(width: 65.w),
          ],
        ),
      ),
    ]);
  }
}

Widget _buildTextLine(String chinese, String english, {Color? textColor}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        chinese,
        style: TextStyle(
            color: textColor ?? BTN_PRIMARY_COLOR,
            fontSize: 50.w,
            fontWeight: FontWeight.bold,
            height: 1),
      ),
      Text(
        english,
        style: TextStyle(color: BTN_PRIMARY_COLOR, fontSize: 24.w, height: 1),
      ),
    ],
  );
}
