import 'package:decimal/decimal.dart';
import 'package:json_annotation/json_annotation.dart';

part 'discount_model.g.dart'; // 生成的代码文件

@JsonSerializable()
class DiscountModel {
  final int id;
  final int storeId;
  final String name;
  final String type;
  final int piece;
  final Decimal discount;
  final int priority;
  final int createTime;
  final int updateTime;
  final int isDelete;

  DiscountModel({
    required this.id,
    required this.storeId,
    required this.name,
    required this.type,
    required this.piece,
    required this.discount,
    required this.priority,
    required this.createTime,
    required this.updateTime,
    required this.isDelete,
  });

  factory DiscountModel.fromJson(Map<String, dynamic> json) =>
      _$DiscountModelFromJson(json);

  Map<String, dynamic> toJson() => _$DiscountModelToJson(this);
}
