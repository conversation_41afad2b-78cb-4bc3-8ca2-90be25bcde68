import 'package:flutter/services.dart';

class ScreenSize {
  static final methodChannel = const MethodChannel('screen_size');

  // (width, height)
  static Future<(double, double)> getScreenSize() async {
    final Map<dynamic, dynamic> resultData =
        await methodChannel.invokeMethod('getScreenSize');
    if (resultData['width'] is int) {
      int width = resultData['width'];
      int height = resultData['height'];
      return (width.toDouble(), height.toDouble());
    }
    return (resultData['width'] as double, resultData['height'] as double);
  }
}
