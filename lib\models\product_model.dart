import 'package:decimal/decimal.dart';
import 'package:json_annotation/json_annotation.dart';

part 'product_model.g.dart'; // 生成的代码文件

@JsonSerializable()
class ProductModel {
  final int id;
  int quantity;
  final String number;
  final String name;
  final String format;
  final String? type; // 可为空字段
  final Decimal price;
  final String unit;
  final int storeId;
  // final DateTime createTime; // 使用 DateTime
  // final DateTime updateTime; // 使用 DateTime
  final int isDelete;

  ProductModel({
    required this.id,
    required this.quantity,
    required this.number,
    required this.name,
    required this.format,
    this.type,
    required this.price,
    required this.unit,
    required this.storeId,
    // required this.createTime,
    // required this.updateTime,
    required this.isDelete,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProductModelToJson(this);
}
