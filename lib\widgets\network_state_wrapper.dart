import 'package:connectivity_wrapper/connectivity_wrapper.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NetworkStateWrapper extends StatelessWidget {
  final Widget? child;

  const NetworkStateWrapper({
    super.key,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ConnectivityScreenWrapper(
      positionOnScreen: PositionOnScreen.TOP,
      message: "网络中断，请检查网路连接",
      messageStyle: TextStyle(fontSize: 24.w),
      child: child,
      disableInteraction: false,
      height: 90.w,
    );
  }
}
