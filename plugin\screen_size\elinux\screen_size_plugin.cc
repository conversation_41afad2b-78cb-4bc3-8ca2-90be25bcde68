#include "include/screen_size/screen_size_plugin.h"

#include <flutter_linux/flutter_linux.h>
#include <gtk/gtk.h>
#include <sys/utsname.h>

#include <cstring>

#include "screen_size_plugin_private.h"

#define SCREEN_SIZE_PLUGIN(obj) \
  (G_TYPE_CHECK_INSTANCE_CAST((obj), screen_size_plugin_get_type(), \
                              ScreenSizePlugin))

struct _ScreenSizePlugin {
  GObject parent_instance;
};

G_DEFINE_TYPE(ScreenSizePlugin, screen_size_plugin, g_object_get_type())

// Called when a method call is received from Flutter.
static void screen_size_plugin_handle_method_call(
    ScreenSizePlugin* self,
    FlMethodCall* method_call) {
  g_autoptr(FlMethodResponse) response = nullptr;

  const gchar* method = fl_method_call_get_name(method_call);

  if (strcmp(method, "getScreenSize") == 0) {
    GdkRectangle workarea = {0};
    gdk_monitor_get_workarea(gdk_display_get_primary_monitor(gdk_display_get_default()),
                            &workarea);

    g_autoptr(FlValue) result = fl_value_new_map();
    fl_value_set_string_take(result, "width", fl_value_new_float(workarea.width));
    fl_value_set_string_take(result, "height", fl_value_new_float(workarea.height));

    response = FL_METHOD_RESPONSE(fl_method_success_response_new(result));
  } else {
    response = FL_METHOD_RESPONSE(fl_method_not_implemented_response_new());
  }

  fl_method_call_respond(method_call, response, nullptr);
}

FlMethodResponse* get_platform_version() {
  struct utsname uname_data = {};
  uname(&uname_data);
  g_autofree gchar *version = g_strdup_printf("Linux %s", uname_data.version);
  g_autoptr(FlValue) result = fl_value_new_string(version);
  return FL_METHOD_RESPONSE(fl_method_success_response_new(result));
}

static void screen_size_plugin_dispose(GObject* object) {
  G_OBJECT_CLASS(screen_size_plugin_parent_class)->dispose(object);
}

static void screen_size_plugin_class_init(ScreenSizePluginClass* klass) {
  G_OBJECT_CLASS(klass)->dispose = screen_size_plugin_dispose;
}

static void screen_size_plugin_init(ScreenSizePlugin* self) {}

static void method_call_cb(FlMethodChannel* channel, FlMethodCall* method_call,
                           gpointer user_data) {
  ScreenSizePlugin* plugin = SCREEN_SIZE_PLUGIN(user_data);
  screen_size_plugin_handle_method_call(plugin, method_call);
}

void screen_size_plugin_register_with_registrar(FlPluginRegistrar* registrar) {
  ScreenSizePlugin* plugin = SCREEN_SIZE_PLUGIN(
      g_object_new(screen_size_plugin_get_type(), nullptr));

  g_autoptr(FlStandardMethodCodec) codec = fl_standard_method_codec_new();
  g_autoptr(FlMethodChannel) channel =
      fl_method_channel_new(fl_plugin_registrar_get_messenger(registrar),
                            "screen_size",
                            FL_METHOD_CODEC(codec));
  fl_method_channel_set_method_call_handler(channel, method_call_cb,
                                            g_object_ref(plugin),
                                            g_object_unref);

  g_object_unref(plugin);
}
