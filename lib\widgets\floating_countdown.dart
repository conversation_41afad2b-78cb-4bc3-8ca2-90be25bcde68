import 'dart:async';

import 'package:animate_do/animate_do.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FloatingCountdown extends StatefulWidget {
  final int seconds;
  final double? bottom;
  final int maxSeconds;
  final int showRemainSeconds;
  final int warningThreshold;
  final VoidCallback? onCountdownEnd;
  final bool dialogConfirm;

  const FloatingCountdown({
    super.key,
    required this.seconds,
    this.bottom,
    this.onCountdownEnd,
    this.dialogConfirm = false,
    this.maxSeconds = 0, //
    this.showRemainSeconds = 30, //
    this.warningThreshold = 15, //
  });

  @override
  FloatingCountdownState createState() => FloatingCountdownState();
}

class FloatingCountdownState extends State<FloatingCountdown> {
  late int _countdown;
  late Timer _timer;
  late int _showRemainSeconds;
  late int _warningThreshold;
  bool _animate = true;
  bool _showDialog = false;
  late DateTime _firstStartAt;
  final ValueNotifier<int> _countdownNotifier = ValueNotifier<int>(60);

  @override
  void initState() {
    super.initState();
    _countdown = widget.seconds;
    _countdownNotifier.value = _countdown;
    _showRemainSeconds = widget.showRemainSeconds;
    _warningThreshold = widget.warningThreshold;
    _firstStartAt = DateTime.now();
    _startTimer();
  }

  @override
  void dispose() {
    _timer.cancel();
    _countdownNotifier.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_showDialog) {
        _showConfirmDialog();
      }
    });
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 0) {
          _countdown--;
          _countdownNotifier.value = _countdown;

          // 当倒计时达到阈值时显示弹层
          if (_countdown <= _warningThreshold &&
              !_showDialog &&
              widget.dialogConfirm) {
            _showDialog = true;
            _showConfirmDialog();
          }
        } else {
          timer.cancel();
          if (widget.onCountdownEnd != null) {
            widget.onCountdownEnd!();
          } else {
            // Navigator.pop(context);
          }
        }
      });
    });
  }

  void reset() {
    setState(() {
      if (widget.maxSeconds > 0 &&
          DateTime.now().difference(_firstStartAt).inSeconds >
              widget.maxSeconds) {
        return;
      }
      _showDialog = false;
      _timer.cancel();
      _countdown = widget.seconds;
      _countdownNotifier.value = _countdown;
      _startTimer();
    });
  }

  void stop() {
    _timer.cancel();
  }

  @override
  Widget build(BuildContext context) {
    bool isShow = _countdown < _showRemainSeconds && _countdown > 0;

    const duration = Duration(milliseconds: 300);
    const durationA = Duration(milliseconds: 1000);

    return AnimatedPositioned(
        duration: duration,
        curve: Curves.easeInOut,
        right: isShow ? 0 : -100.w,
        bottom: widget.bottom ?? 0.3.sh,
        child: AnimatedOpacity(
            duration: duration,
            opacity: isShow ? 1.0 : 0,
            child: Container(
                constraints: BoxConstraints(minWidth: 80.w),
                decoration: BoxDecoration(
                  color: const Color.fromARGB(255, 255, 205, 56),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8.w),
                      bottomLeft: Radius.circular(8.w)),
                ),
                padding: EdgeInsets.only(
                  left: 10.w,
                  right: 10.w,
                  top: 10.w,
                  bottom: 10.w,
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.alarm,
                      color: const Color.fromARGB(255, 255, 255, 255),
                      size: 56.w,
                    ).tada(
                        duration: durationA,
                        animate: _countdown < _warningThreshold,
                        infinite: true),
                    Text(
                      ".",
                      style: TextStyle(
                          fontSize: 30.w, color: Colors.white, height: 0.2),
                    ),
                    SizedBox(
                      height: 8.w,
                    ),
                    Text(
                      "${_countdown}s",
                      style: TextStyle(
                        fontSize: 35.w,
                        color: _countdown > _warningThreshold
                            ? Colors.white
                            : const Color.fromARGB(255, 255, 46, 46),
                        fontWeight: _countdown > _warningThreshold
                            ? FontWeight.w500
                            : FontWeight.bold,
                      ),
                    ),
                  ],
                ))));
  }

  void _showConfirmDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ValueListenableBuilder<int>(
          valueListenable: _countdownNotifier,
          builder: (context, value, child) {
            return AlertDialog(
              backgroundColor: const Color.fromARGB(255, 79, 79, 79),
              content: Container(
                width: 0.68.sw,
                height: 200.w,
                child: Column(
                  children: [
                    Text(
                      '支付倒计时$value秒，超时后需重新拍照',
                      style: TextStyle(color: Colors.white, fontSize: 45.w),
                    ),
                    Text('$value-sec countdown, retake if timeout',
                        style: TextStyle(color: Colors.white, fontSize: 30.w)),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  style: ElevatedButton.styleFrom(
                    side: const BorderSide(
                        color: Color.fromARGB(20, 255, 255, 255)),
                    foregroundColor: const Color.fromARGB(255, 216, 216, 216),
                    padding: EdgeInsets.fromLTRB(
                      30.w,
                      30.w,
                      30.w,
                      30.w,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(60.w),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text('重新拍摄',
                          style:
                              TextStyle(color: Colors.white, fontSize: 26.w)),
                      Text('Retake',
                          style:
                              TextStyle(color: Colors.white, fontSize: 18.w)),
                    ],
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    if (widget.onCountdownEnd != null) {
                      widget.onCountdownEnd!();
                    }
                  },
                ),
                TextButton(
                  style: ElevatedButton.styleFrom(
                    foregroundColor: const Color.fromARGB(255, 167, 227, 255),
                    backgroundColor: const Color.fromARGB(255, 255, 255, 255),
                    padding: EdgeInsets.fromLTRB(
                      100.w,
                      30.w,
                      100.w,
                      30.w,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(60.w),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text('继续支付',
                          style: TextStyle(
                              color: Colors.lightBlue, fontSize: 30.w)),
                      Text('Continue Payment',
                          style: TextStyle(
                              color: Colors.lightBlue, fontSize: 18.w)),
                    ],
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    reset();
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}
