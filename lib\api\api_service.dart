// lib/api/api_service.dart
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:kadaya/api/exception.dart';
import 'package:kadaya/models/config_model.dart';
import 'package:kadaya/models/discount_model.dart';
import 'package:kadaya/models/product_model.dart';
import 'package:kadaya/models/style_model.dart';
import 'package:kadaya/utils/global.dart';
import 'package:kadaya/utils/toast.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  late final Dio _dio;
  final defaultRequestError = ApiException(code: "-1", message: "请求异常，请检查网络！");
  final defaultNetError = ApiException(code: "-2", message: "网络异常，请检查网络！");
  String userAgent = '';
  bool isInited = false;

  factory ApiService() {
    return _instance;
  }

  Future init() async {
    if (isInited) return;
    isInited = true;

    String appVersion = Global.instance.appVersion;
    userAgent =
        'flutter/${appVersion} ${Platform.operatingSystem} ${Platform.localHostname}';

    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isLinux) {
      LinuxDeviceInfo linuxInfo = await deviceInfo.linuxInfo;
      userAgent =
          'flutter/$appVersion ${linuxInfo.id}/${linuxInfo.versionId} (${Platform.localHostname})';
    } else if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      userAgent =
          'flutter/$appVersion android/${androidInfo.version} (${Platform.localHostname}; ${androidInfo.brand} ${androidInfo.model})';
    }

    _dio = Dio(BaseOptions(
      baseUrl: Global.instance.isOnline
          ? 'https://api-magicscreen.xcastle.net'
          : 'https://test-api-magicscreen.xcastle.net',
      // baseUrl: 'http://127.0.0.1:8080',
      connectTimeout: const Duration(seconds: 20),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'User-Agent': userAgent,
      },
    ));

    String truncateTextUnicode(String text, {int maxLength = 300}) {
      if (text.runes.length <= maxLength) return text;
      var truncated = String.fromCharCodes(text.runes.take(maxLength));
      return truncated + '...';
    }

    // 添加拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        print(
            '[dio] ${options.method} ${options.uri} ${options.data == null ? "" : "payload: ${truncateTextUnicode(options.data.toString())}"}');
        return handler.next(options);
      },
      onResponse: (response, handler) {
        print(
            '[dio] response: ${response.statusCode} ${truncateTextUnicode(response.toString())}');
        return handler.next(response);
      },
      onError: (DioException e, handler) {
        print('[dio] Error: ${e.message}');
        return handler.next(e);
      },
    ));
  }

  ApiService._internal();

  Future<Response> get(String path, {Map<String, dynamic>? params}) async {
    try {
      final response = await _dio.get(path, queryParameters: params);
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> post(String path, {Map<String, dynamic>? data}) async {
    Options options = Options();
    options.contentType = 'application/json';
    try {
      final response = await _dio.post(path,
          data: data,
          options: Options(
            contentType: 'application/json',
          ));
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> postFormData(String path, FormData formData) async {
    try {
      final response = await _dio.post(
        path,
        data: formData,
        options: Options(
          contentType: 'multipart/form-data',
        ),
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response> getWrapper(String path,
      {Map<String, dynamic>? params, Duration? timeout}) async {
    try {
      Response response;
      if (timeout != null) {
        response =
            await _dio.get(path, queryParameters: params).timeout(timeout);
      } else {
        response = await _dio.get(path, queryParameters: params);
      }
      if (response.statusCode != 200) {
        throw defaultRequestError;
      }
      if (response.data['code'] == '100') {
        return response;
      }
      String msg = response.data['msg'] ?? response.data['message'];
      throw ApiException(code: response.data['code'], message: msg);
    } catch (e) {
      print("err $e");
      String msg = e is ApiException ? e.message : defaultNetError.message;
      Toast.error(null, msg);
      if (e is ApiException) {
        rethrow;
      }
      throw defaultNetError;
    }
  }

  Future<Response> postWrapper(String path,
      {Map<String, dynamic>? data, Duration? timeout}) async {
    try {
      Response response;
      if (timeout != null) {
        response = await _dio.post(path, data: data).timeout(timeout);
      } else {
        response = await _dio.post(path, data: data);
      }
      if (response.statusCode != 200) {
        throw defaultRequestError;
      }
      // msg -> message 统一到message
      response.data['message'] =
          response.data['msg'] ?? response.data['message'];
      if (response.data['code'] == '100') {
        return response;
      }
      throw ApiException(
          code: response.data['code'], message: response.data['message']);
    } catch (e) {
      print("err $e");
      String msg = e is ApiException ? e.message : defaultNetError.message;
      Toast.error(null, msg);
      if (e is ApiException) {
        rethrow;
      }
      throw defaultNetError;
    }
  }

  Future<List<StyleModel>> getStyleList() async {
    final response = await getWrapper(
        '/mp/style/list?screenNumber=${Global.instance.screenNumber}');
    final List<dynamic> data = response.data['data'];
    List<StyleModel> styleModels =
        data.map((json) => StyleModel.fromJson(json)).toList();
    return styleModels;
  }

  Future fillPic(String orderNo) async {
    final response = await postWrapper('/mpBusinessOrder/fillIn',
        data: {'orderNo': orderNo, 'async': true});
    return response.data['data'];
  }

  Future getConfig() async {
    final response = await getWrapper(
        '/mp/screen/config?screenNumber=${Global.instance.screenNumber}');

    final Map<String, dynamic> data = response.data['data'];
    AppConfig config = AppConfig.fromJson(data);
    return config;
  }

  Future getDiscountList() async {
    final response = await getWrapper(
        '/mp/screen/discount?screenNumber=${Global.instance.screenNumber}');
    final List<dynamic> data = response.data['data'];
    List<DiscountModel> list =
        data.map((json) => DiscountModel.fromJson(json)).toList();
    return list;
  }

  Future getPriceList() async {
    final response = await getWrapper(
        '/mp/screen/goods?screenNumber=${Global.instance.screenNumber}');

    final List<dynamic> data = response.data['data'];
    List<ProductModel> list =
        data.map((json) => ProductModel.fromJson(json)).toList();
    return list;
  }

  Future createOrder(Map<String, dynamic> orderData) async {
    final response = await postWrapper('/mp/order/create',
        data: orderData, timeout: const Duration(seconds: 10));
    return response.data;
  }

  Future checkOrder(String orderId) async {
    final response = await getWrapper(
        '/mp/order/details?businessOrderId=$orderId',
        timeout: const Duration(seconds: 10));
    return response.data; // Assuming the server returns the created order data
  }

  Future miniCode(String orderId) async {
    final response =
        await getWrapper('/mp/order/miniQrCode?businessOrderId=$orderId')
            .timeout(const Duration(seconds: 10));
    return response.data;
  }
}
