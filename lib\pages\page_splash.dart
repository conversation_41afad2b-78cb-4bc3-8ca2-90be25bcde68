import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:kadaya/api/algorithm_service.dart';
import 'package:kadaya/api/api_service.dart';
import 'package:kadaya/api/exception.dart';
import 'package:kadaya/models/config_model.dart';
import 'package:kadaya/models/style_model.dart';
import 'package:kadaya/pages/page_home.dart';
import 'package:kadaya/utils/constants.dart';
import 'package:kadaya/utils/file_saver.dart';
import 'package:kadaya/utils/global.dart';
import 'package:kadaya/utils/toast.dart';
import 'package:kadaya/utils/util.dart';
import 'package:kadaya/widgets/config_dialog.dart';
import 'package:kadaya/widgets/network_state_wrapper.dart';
import 'package:path_provider/path_provider.dart';
import 'package:yaml/yaml.dart';

class PageSplash extends StatefulWidget {
  const PageSplash({super.key});

  @override
  State<PageSplash> createState() => _PageSplashState();
}

class _PageSplashState extends State<PageSplash> {
  // Loading states
  bool _isLoadingNetworkData = true;
  bool _isLoadingImages = false;
  bool _isLoadingPreload = false;

  // Success states
  bool _networkDataSuccess = false;
  bool _imagesSuccess = false;
  bool _preloadSuccess = false;

  // Error messages
  String _networkErrorMessage = '';
  String _imagesErrorMessage = '';
  String _preloadErrorMessage = '';

  // tip
  String _imagesStepTip = '';
  String _preloadTip = '';

  // List of files to cache
  List<Map<String, dynamic>> _filesToCache = [];

  static const String configFileName = 'config.yaml';
  bool showConfigPop = false;

  Timer? _timer;
  int _countdown = 5;

  @override
  void initState() {
    super.initState();
    // if (Platform.isLinux){
    //   SoundPlayer().playLocalAsset('assets/audios/photoselectWidget.wav');
    //   return;
    // }
    loadConfig();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> loadConfig() async {
    if (Platform.isLinux) {
      checkAndCreateDirectory('/data/files/screen');
      checkAndCreateDirectory('/data/flutter/logs');
      final config = await readConfigFile('/data/global.yaml');
      Global.instance.screenNumber = config['screenNum']!;
      Global.instance.isOnline = config['env'] == 'prod';
      Global.instance.filePath = APP_PATH_FILES;

      _initializeApp();
    } else if (Platform.isAndroid) {
      bool isExist = await configFileExists();
      final path = await _configFilePath;
      final directory = await getApplicationDocumentsDirectory();
      Global.instance.filePath = directory.path;
      if (!isExist) {
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => ConfigDialog(
                  onDismiss: (isSaved, data) async {
                    if (data != null) {
                      String yaml = mapToYaml(data);
                      try {
                        final file = File(path);
                        var result = await file.writeAsString(yaml);

                        loadConfig();
                      } catch (e) {
                        print('Error writing default config: $e');
                      }
                      print('Dialog data: $data');
                    } else {
                      loadConfig();
                    }
                  },
                  defaultData: Global.instance.nowConfigMap(),
                ));
      } else {
        final config = await readConfigFile(path);
        Global.instance.screenNumber = config['screenNum']!.toString();
        Global.instance.deviceCode =
            config['deviceCode'] != null ? config['deviceCode'].toString() : '';
        Global.instance.isOnline = config['env'] == 'prod';
        _initializeApp();
      }
    }
    // _initializeApp();
  }

  // 获取配置文件路径
  static Future<String> get _configFilePath async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/$configFileName';
  }

  // 检查配置文件是否存在
  static Future<bool> configFileExists() async {
    final path = await _configFilePath;
    return File(path).exists();
  }

  void checkAndCreateDirectory(directoryPath) async {
    // 定义目录路径
    // String directoryPath = '/data/files/screen';

    // 创建一个 Directory 对象
    Directory directory = Directory(directoryPath);

    // 检查目录是否存在
    if (!await directory.exists()) {
      // 如果目录不存在，则创建它
      await directory.create(recursive: true); // recursive: true 会创建所有不存在的父目录
      print('目录已创建: $directoryPath');
    } else {
      print('目录已存在: $directoryPath');
    }
  }

// 读取配置文件
  Future<Map<String, dynamic>> readConfigFile(String filePath) async {
    try {
      // 读取文件内容
      final file = File(filePath);
      final contents = await file.readAsString();

      // 解析文件内容为键值对
      final YamlMap yamlMap = loadYaml(contents);
      final Map<String, dynamic> configMap = yamlMap.cast<String, dynamic>();
      return configMap;
    } catch (e) {
      return {'screenNum': '001'};
      // throw Exception('Failed to read or parse file: $e');
    }
  }

// 解析配置文件内容
  Map<String, String> _parseConfig(String contents) {
    final configMap = <String, String>{};

    // 按行分割文件内容
    final lines = contents.split('\n');

    // 遍历每一行，解析键值对
    for (final line in lines) {
      if (line.contains('=')) {
        // 按等号分割键和值
        final parts = line.split('=');
        if (parts.length == 2) {
          final key = parts[0].trim(); // 键
          final value = parts[1].trim(); // 值
          configMap[key] = value;
        }
      }
    }

    return configMap;
  }

  // Initialize app data
  Future<void> _initializeApp() async {
    // api init
    await ApiService().init();

    try {
      // Read camera direction from local storage
      if (Platform.isLinux) {
        final value = await FileSaver.readJsonFile(APP_PATH_FLUTTER);
        Global.instance.cameraDirection = value['direction'] ?? 1;
      }

      // Load network data
      await _loadNetworkData();
    } catch (e) {
      print("err: $e");
      setState(() {
        _networkErrorMessage = e.toString();
      });
      _startCountdown();
    }
  }

  // Step 1: Load network data
  Future<void> _loadNetworkData() async {
    setState(() {
      _isLoadingNetworkData = true;
      _networkErrorMessage = '';
      _networkDataSuccess = false;
    });

    try {
      // Initialize storage service

      // Fetch all required data from APIs

      final discounts = await ApiService().getDiscountList();
      final products = await ApiService().getPriceList();

      // Store data in global instance

      Global.instance.discounts = discounts;
      Global.instance.products = products;

      // Mark network data loading as successful
      setState(() {
        _isLoadingNetworkData = false;
        _networkDataSuccess = true;
        _isLoadingImages = true; // Start loading images
      });

      // Proceed to cache images
    } catch (e) {
      if (e is! ApiException) {
        print("err: $e");
        Toast.error(null, e.toString());
      }
      setState(() {
        _isLoadingNetworkData = false;
        _networkErrorMessage = e.toString();
      });
      _startCountdown();
      return;
    }

    _cacheImages();
  }

  // Step 2: Cache images
  Future<void> _cacheImages() async {
    setState(() {
      _isLoadingImages = true;
      _imagesErrorMessage = '';
      _imagesSuccess = false;
    });

    try {
      final styles = await ApiService().getStyleList();
      AppConfig config = await ApiService().getConfig();

      Global.instance.styleModels = styles;
      Global.instance.appConfig = config;
      // Prepare list of files to cache
      _filesToCache = [];

      // Add style images
      for (final item in styles) {
        _filesToCache.add({
          'url': item.picCover,
          'fileName':
              "style/${FileSaver.getFileNameWithoutExtension(item.picCover)}.jpg",
        });

        for (final obj in item.picCollect) {
          _filesToCache
              .add({'url': obj.url, 'fileName': "style/${obj.uuid}.jpg"});
        }
      }

      // Add config images
      _filesToCache.add({
        'url': config.headImage,
        'fileName':
            "style/${FileSaver.getFileNameWithoutExtension(config.headImage)}.jpg",
      });
      for (final url in config.carouselImages) {
        _filesToCache.add({
          'url': url,
          'fileName': "style/${FileSaver.getFileNameWithoutExtension(url)}.jpg",
        });
      }

      // sounds
      Map<String, String> nowSounds = {};
      if (config.sounds != null) {
        for (final item in config.sounds!.entries) {
          final fileName =
              "assets/${item.key}.${Uri.parse(item.value).path.split('.').last}";
          _filesToCache.add({
            'url': item.value,
            'fileName': fileName,
          });
          nowSounds[item.key] = fileName;
        }
      }
      Global.instance.nowSounds = nowSounds;

      if (_filesToCache.isEmpty) {
        setState(() {
          _isLoadingImages = false;
          _imagesSuccess = true;
        });
        _navigateToHome();
        return;
      }

      final stream = FileSaver.saveFilesConcurrently(files: _filesToCache);

      stream.listen(
        (event) {
          if (event.containsKey('index')) {
            // Real-time progress
            print(
                'File ${event['index']}/${event['total']} result: ${event['result']}');

            setState(() {
              _imagesStepTip = "${event['index']}/${event['total']}";
            });
          } else {
            // Final result
            print(
                'All files completed. Success: ${event['success']}, Failure: ${event['failure']}');

            if (event['failure'] > 0) {
              setState(() {
                _isLoadingImages = false;
                _imagesErrorMessage = '${event['failure']} 个文件缓存失败';
              });
            } else {
              setState(() {
                _isLoadingImages = false;
                _imagesSuccess = true;
              });
              _preloadData();
            }
          }
        },
        onError: (error) {
          print('Error: $error');
          setState(() {
            _isLoadingImages = false;
            _imagesErrorMessage = error.toString();
          });
          _startCountdown();
        },
        onDone: () {
          print('Stream closed');
        },
      );
    } catch (e) {
      if (e is! ApiException) {
        print('Error: $e');
        Toast.error(null, e.toString());
      }
      setState(() {
        _isLoadingImages = false;
        _imagesErrorMessage = e.toString();
      });
      _startCountdown();
    }
  }

  void _preloadData() {
    if (Platform.isAndroid) {
      _navigateToHome();
      return;
    }
    setState(() {
      _isLoadingPreload = true;
      _preloadErrorMessage = '';
      _preloadSuccess = false;
    });

    List<String> list = [];

    for (StyleModel styleModel in Global.instance.styleModels) {
      for (PicCollect picCollect in styleModel.picCollect) {
        list.add('/data/files/style/${picCollect.uuid}.jpg');
      }
    }

    setState(() {
      _isLoadingPreload = true;
    });

    // 进度预估  32核cpu平均0.2s/p
    double estimateSeconds =
        0.2 * 32 / Platform.numberOfProcessors * list.length;
    DateTime begeinAt = DateTime.now();
    Timer timerPreload = Timer.periodic(const Duration(seconds: 1), (timer) {
      double nowSeconds =
          DateTime.now().difference(begeinAt).inSeconds.toDouble();
      int percent = min(99, (nowSeconds / estimateSeconds * 100).toInt());
      setState(() {
        _preloadTip = '$percent%';
      });
    });
    try {
      print('algorithm preload');
      // 100ms
      NetworkUtils.postData(
              'http://127.0.0.1:8082/algorithmPreload', {'imagePath': list})
          .then((result) {
        timerPreload.cancel();
        if (result['code'] == '200') {
          setState(() {
            _isLoadingPreload = false;
            _preloadSuccess = true;
          });
          _navigateToHome();
        } else {
          Toast.error(null, result['error'] ?? '预热错误');
          setState(() {
            _preloadSuccess = false;
            _isLoadingPreload = false;
            _preloadErrorMessage = result['error'] ?? '预热错误';
          });
          _startCountdown();
        }
      });
    } catch (e) {
      print("err: $e");
      timerPreload.cancel();
    }
  }

  // Navigate to home page
  void _navigateToHome() {
    if (!mounted) return;

    if (_networkDataSuccess && _imagesSuccess) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => PageHome(),
        ),
      );
    }
  }

  // Retry all failed steps
  void _retryAll() {
    _timer?.cancel();
    if (!_networkDataSuccess) {
      _loadNetworkData();
    } else if (!_imagesSuccess) {
      _cacheImages();
    } else if (!_preloadSuccess) {
      _preloadData();
    }
  }

  void _startCountdown() {
    _timer?.cancel();
    setState(() => _countdown = 5);

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 0) {
          _countdown--;
        } else {
          timer.cancel();
          _retryAll();
        }
      });
    });
  }

  void showSetting() async {
    final path = await _configFilePath;
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => ConfigDialog(
              onDismiss: (isSaved, data) async {
                if (data != null) {
                  String yaml = mapToYaml(data);
                  try {
                    final file = File(path);
                    var result = await file.writeAsString(yaml);
                  } catch (e) {
                    print('Error writing default config: $e');
                  }
                  print('Dialog data: $data');

                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => PageSplash(),
                    ),
                  );
                } else {}
              },
              defaultData: Global.instance.nowConfigMap(),
            ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NetworkStateWrapper(
        child: Stack(
          children: [
            Container(
              // decoration: const BoxDecoration(
              //   image: DecorationImage(
              //     image: AssetImage('assets/images/splash.png'),
              //     fit: BoxFit.cover,
              //   ),
              // ),
              child: Center(
                child: Container(
                  padding: EdgeInsets.all(40.w),
                  margin: EdgeInsets.symmetric(horizontal: 30.w),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(10.w),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '应用初始化',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 36.w,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 60.w),

                      // Step 1: Network data loading
                      _buildLoadingStep(
                        title: '加载网络数据',
                        isLoading: _isLoadingNetworkData,
                        isSuccess: _networkDataSuccess,
                        errorMessage: _networkErrorMessage,
                      ),

                      SizedBox(height: 40.w),

                      // Step 2: Image caching
                      _buildLoadingStep(
                        title: '缓存静态资源',
                        isLoading: _isLoadingImages,
                        isSuccess: _imagesSuccess,
                        errorMessage: _imagesErrorMessage,
                        stepTip: _imagesStepTip,
                      ),

                      SizedBox(height: 40.w),

                      // Step 2: Image caching
                      _buildLoadingStep(
                        title: '应用加速',
                        isLoading: _isLoadingPreload,
                        isSuccess: _preloadSuccess,
                        errorMessage: _preloadErrorMessage,
                        stepTip: _preloadTip,
                      ),

                      SizedBox(height: 60.w),

                      // Retry button (only shown when there's an error)
                      if (_networkErrorMessage.isNotEmpty ||
                          _imagesErrorMessage.isNotEmpty ||
                          _preloadErrorMessage.isNotEmpty)
                        ElevatedButton(
                          onPressed: _retryAll,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black,
                            padding: EdgeInsets.symmetric(
                                horizontal: 60.w, vertical: 24.w),
                          ),
                          child: Text('Retry ${_countdown}s'),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            if (Platform.isAndroid)
              Positioned(
                right: 30.w,
                top: 30.w,
                child: GestureDetector(
                  onTap: showSetting,
                  child: Text(
                    '设置',
                    style: TextStyle(color: Colors.white, fontSize: 25.sp),
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }

  // Helper method to build each loading step
  Widget _buildLoadingStep({
    required String title,
    required bool isLoading,
    required bool isSuccess,
    required String errorMessage,
    String? stepTip,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              color: Colors.white,
              fontSize: 30.w,
            ),
          ),
        ),
        SizedBox(width: 20.w),
        if (isLoading && stepTip != null && stepTip.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(right: 30.0.w),
            child: Text(stepTip,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 30.w,
                )),
          ),
        if (isLoading)
          SizedBox(
            width: 40.w,
            height: 40.w,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 2.w,
            ),
          )
        else if (errorMessage.isNotEmpty)
          Icon(
            Icons.close,
            color: Colors.red,
            size: 40.w,
          )
        else if (isSuccess)
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 40.w,
          )
        else
          SizedBox(width: 40.w, height: 40.w),
      ],
    );
  }
}
