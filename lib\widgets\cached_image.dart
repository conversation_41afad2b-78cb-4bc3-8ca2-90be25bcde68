import 'dart:async';
import 'dart:io';

import 'package:crypto/crypto.dart' as crypto;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class CachedNetworkImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Widget? retryWidget;
  final Duration? placeholderFadeInDuration;
  final Duration? imageFadeInDuration;
  final int maxRetryCount;

  const CachedNetworkImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
    this.placeholder,
    this.errorWidget,
    this.retryWidget,
    this.placeholderFadeInDuration,
    this.imageFadeInDuration,
    this.maxRetryCount = 2,
  }) : super(key: key);

  @override
  _CachedNetworkImageState createState() => _CachedNetworkImageState();
}

class _CachedNetworkImageState extends State<CachedNetworkImage> {
  late Future<File> _imageFileFuture;
  int _retryCount = 0;
  bool _showRetryButton = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(CachedNetworkImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _resetState();
      _loadImage();
    }
  }

  void _resetState() {
    setState(() {
      _retryCount = 0;
      _showRetryButton = false;
    });
  }

  void _loadImage() {
    setState(() {
      _imageFileFuture = _getLocalImageFile();
      _showRetryButton = false;
    });
  }

  // 获取本地图片文件，如果没有则下载
  Future<File> _getLocalImageFile() async {
    final fileName = _getFileNameFromUrl(widget.imageUrl);
    final dir = await getTemporaryDirectory();

    final fullPath = '${dir.path}/temp';
    final directory = Directory(fullPath);

    final exists = await directory.exists();

    if (!exists) {
      // 如果不存在则创建目录（包括所有不存在的父目录）
      directory.create(recursive: true);
    }
    final file = File('${directory.path}/$fileName');

    if (await file.exists()) {
      return file;
    }

    // 如果本地不存在，则下载并保存
    return _downloadAndSaveImageWithRetry(widget.imageUrl, file);
  }

  // 带重试机制的下载方法
  Future<File> _downloadAndSaveImageWithRetry(String url, File file) async {
    while (_retryCount <= widget.maxRetryCount) {
      try {
        final response = await http.get(Uri.parse(url));
        if (response.statusCode != 200) {
          throw Exception('Failed to download image: ${response.statusCode}');
        }

        await file.writeAsBytes(response.bodyBytes);
        return file;
      } catch (e) {
        _retryCount++;

        // 如果达到最大重试次数，抛出异常
        if (_retryCount > widget.maxRetryCount) {
          // 如果下载失败，删除可能创建的空文件
          if (await file.exists()) {
            await file.delete();
          }

          // 显示重试按钮
          if (mounted) {
            setState(() {
              _showRetryButton = true;
            });
          }

          throw e;
        }

        // 等待一段时间后重试
        await Future.delayed(const Duration(seconds: 1));
      }
    }

    // 理论上不会执行到这里
    throw Exception(
        'Failed to download image after ${widget.maxRetryCount} retries');
  }

  // 从URL生成唯一的文件名
  String _getFileNameFromUrl(String url) {
    final uri = Uri.parse(url);
    final hash = crypto.md5.convert(uri.toString().codeUnits).toString();
    return 'image_$hash${uri.pathSegments.last}';
  }

  // 处理重试按钮点击
  void _handleRetry() {
    _resetState();
    _loadImage();
  }

  @override
  Widget build(BuildContext context) {
    if (_showRetryButton) {
      return GestureDetector(
        onTap: _handleRetry,
        child: widget.retryWidget ?? _buildRetryWidget(),
      );
    }

    return FutureBuilder<File>(
      future: _imageFileFuture,
      builder: (BuildContext context, AsyncSnapshot<File> snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasError) {
            return widget.errorWidget ?? _buildErrorWidget();
          }
          return Image.file(
            snapshot.data!,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            // cacheWidth: widget.width?.toInt(),
            // cacheHeight: widget.height?.toInt(),
          );
        } else {
          return widget.placeholder ?? _buildPlaceholder();
        }
      },
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[200],
      child: const Center(
        child: Icon(Icons.image, color: Colors.grey),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[200],
      child: const Center(
        child: Icon(Icons.broken_image, color: Colors.grey),
      ),
    );
  }

  Widget _buildRetryWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.refresh, color: Colors.grey, size: 40),
          const SizedBox(height: 8),
          Text(
            '点击重试',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
