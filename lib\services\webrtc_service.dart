import 'dart:async';
import 'dart:collection';
import 'dart:isolate';
import 'dart:typed_data';

import 'package:flutter/services.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

class WebRTCService {
  MediaStream? _nowStream;
  Function(MediaStream?)? _streamChangeCallback;
  Function((int, int, ByteBuffer)?)?
      _captureFrameCallback; // (width, height, ByteBuffer_rgba)
  SendPort? _childSendPort;
  // Timer? _watchDeviceTimer;

  initialize() async {
    ReceivePort mainReceivePort = ReceivePort();
    mainReceivePort.listen((data) async {
      if (data is SendPort) {
        _childSendPort = data;
        return;
      }

      var params = data as (String, dynamic);
      var event = params.$1;
      switch (event) {
        case "stream":
          _nowStream = params.$2;
          _streamChangeCallback?.call(_nowStream);
          break;
        case "captureFrame":
          _captureFrameCallback?.call(params.$2);
          break;
      }
    });

    var rootIsolateToken = RootIsolateToken.instance!;

    List<dynamic> params = List.empty(growable: true);
    params.add(mainReceivePort.sendPort);
    params.add(rootIsolateToken);

    await Isolate.spawn((List<dynamic> params) async {
      RootIsolateToken rootIsolateToken = params[1] as RootIsolateToken;
      BackgroundIsolateBinaryMessenger.ensureInitialized(rootIsolateToken);

      MediaStream? nowStream;

      final Map<String, dynamic> mediaConstraints = {
        'audio': false,
        'video': true,
      };

      Future<MediaStream?> getStream() async {
        final response = await WebRTC.invokeMethod(
          'getUserMedia',
          <String, dynamic>{'constraints': mediaConstraints},
        );
        if (response == null) {
          return null;
        } else {
          String streamId = response['streamId'];
          var stream = MediaStreamNative(streamId, 'local');
          stream.setMediaTracks(
              response['audioTracks'] ?? [], response['videoTracks'] ?? []);
          return stream;
        }
      }

      SendPort mainPort = params[0] as SendPort;

      ReceivePort childReceivePort = ReceivePort();
      childReceivePort.listen((data) async {
        var params = data as (String, dynamic);
        var event = params.$1;
        switch (event) {
          case "deviceChange":
            var deviceCount = params.$2 as int;
            print("webrtc: trigger device change. deviceCount:${deviceCount}");
            nowStream?.dispose();
            nowStream = null;
            if (deviceCount == 0) {
              print("webrtc: stream send null");
              mainPort.send(('stream', null));
              return;
            }
            mainPort.send(('stream', nowStream = await getStream()));
            print("webrtc: stream send ${nowStream != null}");
            break;
          case "captureFrame":
            print("webrtc: captureFrame, nowStream is ${nowStream != null}");
            var videoTracks = nowStream?.getVideoTracks();
            if (videoTracks == null || videoTracks.isEmpty) {
              mainPort.send(('captureFrame', null));
              return;
            } else {
              var width = videoTracks.first.getSettings()['width'] as int;
              var height = videoTracks.first.getSettings()['height'] as int;

              final frame = await videoTracks.first.captureFrame();
              if (frame.lengthInBytes == 0) {
                mainPort.send(('captureFrame', null));
              } else {
                mainPort.send(('captureFrame', (width, height, frame)));
              }
            }
        }
      });
      mainPort.send(childReceivePort.sendPort);
      mainPort.send(('stream', nowStream = await getStream()));
    }, params);

    // watch deviceChange
    Set<String> lastVideoDeviceIds = await getVideoDeviceIds();
    Timer.periodic(const Duration(seconds: 1), (timer) async {
      Set<String> videoDeviceIds = await getVideoDeviceIds();
      if (videoDeviceIds.length != lastVideoDeviceIds.length ||
          !videoDeviceIds.containsAll(lastVideoDeviceIds)) {
        print(
            "WatchDeviceChange: videoDevice change, nowDeviceIds: ${videoDeviceIds}");
        lastVideoDeviceIds = videoDeviceIds;
        _childSendPort?.send(('deviceChange', videoDeviceIds.length));
      }
    });
  }

  MediaStream? getStream() {
    return _nowStream;
  }

  setStreamChangeCallback(Function(MediaStream?)? callback) {
    _streamChangeCallback = callback;
  }

  // triggerDeviceChange() {
  //   _childSendPort?.send("deviceChange");
  // }

  Future<Set<String>> getVideoDeviceIds() async {
    Set<String> videoDeviceIds = HashSet<String>();
    List<MediaDeviceInfo> devices =
        await navigator.mediaDevices.enumerateDevices();
    for (MediaDeviceInfo device in devices) {
      if (device.kind != "videoinput") {
        continue;
      }
      videoDeviceIds.add(device.deviceId);
    }
    return videoDeviceIds;
  }

  Future<(int, int, ByteBuffer)?> captureFrame() async {
    Future<(int, int, ByteBuffer)?> promiseWapper() {
      final completer = Completer<(int, int, ByteBuffer)?>();
      _captureFrameCallback = ((int, int, ByteBuffer)? value) {
        completer.complete(value);
      };
      _childSendPort?.send(("captureFrame", null));
      return completer.future;
    }

    return await promiseWapper();
  }
}
