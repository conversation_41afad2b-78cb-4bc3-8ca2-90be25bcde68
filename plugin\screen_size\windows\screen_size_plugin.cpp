#include "screen_size_plugin.h"

// This must be included before many other Windows headers.
#include <windows.h>

// For getPlatformVersion; remove unless needed for your plugin implementation.
#include <VersionHelpers.h>

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>

#include <memory>
#include <sstream>

namespace screen_size {

// static
void ScreenSizePlugin::RegisterWithRegistrar(
    flutter::PluginRegistrarWindows *registrar) {
  auto channel =
      std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
          registrar->messenger(), "screen_size",
          &flutter::StandardMethodCodec::GetInstance());

  auto plugin = std::make_unique<ScreenSizePlugin>();

  channel->SetMethodCallHandler(
      [plugin_pointer = plugin.get()](const auto &call, auto result) {
        plugin_pointer->HandleMethodCall(call, std::move(result));
      });

  registrar->AddPlugin(std::move(plugin));
}

ScreenSizePlugin::ScreenSizePlugin() {}

ScreenSizePlugin::~ScreenSizePlugin() {}

void ScreenSizePlugin::HandleMethodCall(
    const flutter::MethodCall<flutter::EncodableValue> &method_call,
    std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
  if (method_call.method_name().compare("getScreenSize") == 0) {
      int width = GetSystemMetrics(SM_CXSCREEN);
      int height = GetSystemMetrics(SM_CYSCREEN);

      flutter::EncodableMap resultMap = flutter::EncodableMap();
      resultMap[flutter::EncodableValue("width")] = flutter::EncodableValue(width);
      resultMap[flutter::EncodableValue("height")] = flutter::EncodableValue(height);

      result->Success(resultMap);
    } else {
      result->NotImplemented();
    }

}

}  // namespace screen_size
