import 'dart:io';

import 'package:connectivity_wrapper/connectivity_wrapper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fvp/fvp.dart' as fvp;
import 'package:fvp/mdk.dart' as mdk;
import 'package:kadaya/pages/page_splash.dart';
import 'package:kadaya/services/webrtc_service.dart';
import 'package:kadaya/utils/global.dart';
import 'package:screen_size/screen_size.dart';
import 'package:toastification/toastification.dart';
import 'package:window_manager/window_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  fvp.registerWith(options: {
    'platforms': ['linux']
  });
  mdk.setLogHandler((level, msg) {});
  mdk.setGlobalOption("logLevel", "Error");

  // if (Platform.isAndroid) {
  //   MediaKit.ensureInitialized();
  // }

  // 错误
  FlutterError.onError = (FlutterErrorDetails details) {
    print("FlutterError: $details");
  };
  //
  PlatformDispatcher.instance.onError = (error, stack) {
    print("PlatformDispatcherError: err: $error, stack: $stack");
    return true;
  };

  if (Platform.isLinux) {
    await windowManager.ensureInitialized();

    var (width, height) = await ScreenSize.getScreenSize();
    print("screenSize w:${width}, h:${height}");

    if (width < height) {
      WindowOptions windowOptions = const WindowOptions(
        fullScreen: true,
        // titleBarStyle: TitleBarStyle.hidden,
      );
      windowManager.waitUntilReadyToShow(windowOptions, () async {
        // await windowManager.setPosition(const Offset(0, 0));
        // await windowManager.setResizable(false); // 这个会引起窗口不能变大!
        await windowManager.show();
        await windowManager.focus();
      });
    } else {
      Global.instance.isRunPc = true;

      WindowOptions windowOptions = const WindowOptions(
        size: Size(500, 500 * 16 / 9),
      );
      windowManager.waitUntilReadyToShow(windowOptions, () async {
        // await windowManager.setResizable(false);
        await windowManager.show();
        await windowManager.focus();
      });
    }
    Global.webRTCService = WebRTCService();
    Global.webRTCService.initialize();
  } else {
    await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  }

  // connectivityWrapper config
  ConnectivityWrapper.instance.addresses = (kIsWeb)
      ? []
      : List<AddressCheckOptions>.unmodifiable(
          <AddressCheckOptions>[
            AddressCheckOptions(
              port: 53,
              address: InternetAddress(
                '*********',
                type: InternetAddressType.IPv4,
              ),
            ),
            AddressCheckOptions(
              port: 53,
              address: InternetAddress(
                '***************',
                type: InternetAddressType.IPv4,
              ),
            ),
          ],
        );

  // Global.instance.mockStepPayment();
  // await ApiService().init();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    if (Platform.isAndroid) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    }

    return ScreenUtilInit(
      designSize: const Size(1080, 1080 * 16 / 9),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) {
        return MouseRegion(
            cursor: kReleaseMode && !Global.instance.isRunPc
                ? SystemMouseCursors.none
                : SystemMouseCursors
                    .alias, // This hides the cursor throughout the app
            child: ConnectivityAppWrapper(
                app: ToastificationWrapper(
                    config: ToastificationConfig(itemWidth: 0.5.sw),
                    // child: MaterialApp(home:CameraScreen()),
                    child: MaterialApp(
                      debugShowCheckedModeBanner: false,
                      navigatorObservers: [Global.instance.routeObserver],
                      theme: ThemeData(
                        // fontFamily: "NotoSans",
                        brightness: Brightness.dark,
                        primaryColor: const Color(0xFFEE3342),
                      ),
                      home: const PageSplash(),
                    ))));
      },
    );
  }

  void initConnectWrapperConfig() {}
}
