#ifdef _MSC_VER
#define _CRT_SECURE_NO_WARNINGS
#endif

#include "flutter_frame_capturer.h"
#include <stdio.h>
#include <stdlib.h>
#include "svpng.hpp"

namespace flutter_webrtc_plugin {

FlutterFrameCapturer::FlutterFrameCapturer(RTCVideoTrack* track,
                                           std::string path) {
  track_ = track;
  path_ = path;
}

void FlutterFrameCapturer::OnFrame(scoped_refptr<RTCVideoFrame> frame) {
  std::lock_guard<std::mutex> lock(mutex_);
  if (frame_ != nullptr) {
    return;
  }
  frame_ = frame.get()->Copy();
  catch_frame_ = true;
  cv_.notify_one();
}

void FlutterFrameCapturer::CaptureFrame(
    std::unique_ptr<MethodResultProxy> result) {
  std::unique_lock<std::mutex> lock(mutex_);
  catch_frame_ = false;
  track_->AddRenderer(this);

  bool caught = cv_.wait_for(lock, std::chrono::seconds(1), [this]() {
    return catch_frame_.load();
  });

  track_->RemoveRenderer(this); 

  if (!caught) {
    result->Error("timeout", "Frame capture timed out.");
    return;
  }

  std::vector<uint8_t> value = SaveFrame();
  result->Success(EncodableValue(value));
}

std::vector<uint8_t> FlutterFrameCapturer::SaveFrame() {
  if (frame_ == nullptr) {
    return std::vector<uint8_t>();
  }

  int width = frame_.get()->width();
  int height = frame_.get()->height();
  int bytes_per_pixel = 4;
  int total_bytes = width * height * bytes_per_pixel;
  uint8_t* pixels = new uint8_t[total_bytes];

  frame_.get()->ConvertToARGB(RTCVideoFrame::Type::kABGR, pixels,
                              /* unused */ -1, width, height);

  std::vector<uint8_t> pixelVector(pixels, pixels + total_bytes);
  delete[] pixels;

  return pixelVector;
}

}  // namespace flutter_webrtc_plugin