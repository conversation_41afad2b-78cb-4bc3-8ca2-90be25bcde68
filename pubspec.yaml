name: kadaya
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.3.4 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # intl: ^0.20.2
  cupertino_icons: ^1.0.6
  flutter_animate: ^4.5.2
  shared_preferences: ^2.5.2
  dio: ^5.8.0+1
  json_annotation: ^4.9.0
  http: ^1.3.0
  intl: ^0.18.1
  qr_flutter: ^4.1.0
  image: ^4.1.3
  window_manager: ^0.3.7
  yaml: ^3.1.3
  toastification: ^3.0.1
  path_provider: ^2.1.1
  camera: ^0.10.0+1
  video_player: ^2.10.0
  fvp: ^0.32.1
  wav: ^1.5.0
  #  media_kit: ^1.2.0
  #  media_kit_video: ^1.2.5 # For video rendering.
  #  media_kit_libs_video: ^1.0.5 # Native video dependencies.
  #  media_kit_libs_linux: ^1.2.1
  just_audio: ^0.9.46
  process_run: ^1.2.4
  connectivity_wrapper: ^1.2.6
  decimal: ^2.3.3
  device_info_plus: ^11.4.0
  flutter_screenutil: ^5.9.3
  cached_network_image: ^3.4.1
  animate_do: ^4.2.0
  gif: ^2.3.0
  lottie: ^3.3.1
  screen_size:
    path: ./plugin/screen_size
  flutter_webrtc:
    path: ./plugin/flutter_webrtc # ^0.13.1+hotfix.1
  card_swiper: 
    path: ./plugin/card_swiper-3.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  build_runner: ^2.4.15
  json_serializable: ^6.9.4


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/gender/
    - assets/audios/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: DouyinSansBold
      fonts:
        - asset: fonts/DouyinSansBold.ttf
    # - family: Trajan Pro
    #   fonts:
    #     - asset: fonts/TrajanPro.ttf
    #     - asset: fonts/TrajanPro_Bold.ttf
    #       weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
